%-------------------------------------------------------------------------%
%--------        DCFT ISAR成像处理器示例                          -------%
%--------        处理距离压缩后的回波数据（shipx2.mat）           -------%
%-------------------------------------------------------------------------%
clc; clear all; close all;

% 1. 加载ISAR回波数据
fprintf('=== DCFT ISAR 处理器示例 ===\n');

% 加载数据 (尝试加载不同格式的数据)
data_loaded = false;

if exist('shipx2.mat', 'file')
    fprintf('加载数据: shipx2.mat\n');
    load('shipx2.mat');
    if exist('shipx2', 'var')
        echo_data = shipx2;
        data_loaded = true;
        fprintf('使用变量: shipx2\n');
    elseif exist('X', 'var')
        echo_data = X;
        data_loaded = true;
        fprintf('使用变量: X\n');
    end
elseif exist('data_ship.mat', 'file')
    fprintf('加载数据: data_ship.mat\n');
    load('data_ship.mat');
    if exist('data_ship', 'var')
        echo_data = data_ship;
        data_loaded = true;
        fprintf('使用变量: data_ship\n');
    end
elseif exist('simulation_ship_s_r_tm2.mat', 'file')
    fprintf('加载数据: simulation_ship_s_r_tm2.mat\n');
    load('simulation_ship_s_r_tm2.mat');
    if exist('s_r_tm2', 'var')
        echo_data = s_r_tm2;
        data_loaded = true;
        fprintf('使用变量: s_r_tm2\n');
    end
end

% 如果没有找到数据，生成模拟数据
if ~data_loaded
    fprintf('未找到数据文件，生成模拟数据...\n');
    
    % 创建简单的模拟散射点阵列
    num_range_bins = 128;
    num_azimuth = 256;
    echo_data = zeros(num_range_bins, num_azimuth);
    
    % 添加几个具有非线性相位变化的散射点
    scatter_points = [
        40, 0.8, 190, 30, 400;  % [距离单元, 幅度, 多普勒中心, 啁啾率, 啁啾率导数]
        60, 0.9, 150, 50, 300;
        80, 0.7, 170, 40, 350;
    ];
    
    % 时间轴
    tm = (0:num_azimuth-1)/num_azimuth;
    
    % 生成回波信号
    for i = 1:size(scatter_points, 1)
        r_idx = scatter_points(i, 1);
        amp = scatter_points(i, 2);
        doppler = scatter_points(i, 3);
        chirp_rate = scatter_points(i, 4);
        chirp_rate_deriv = scatter_points(i, 5);
        
        % 生成相位项
        phase = 2*pi * (doppler*tm + (1/2)*chirp_rate*tm.^2 + (1/6)*chirp_rate_deriv*tm.^3);
        
        % 添加到回波数据
        echo_data(r_idx, :) = amp * exp(1j * phase);
    end
    
    % 添加噪声
    noise_level = 0.05;
    echo_data = echo_data + noise_level * (randn(size(echo_data)) + 1j*randn(size(echo_data)));
    
    fprintf('模拟数据生成完成: %d x %d\n', num_range_bins, num_azimuth);
end

% 2. 设置处理参数
params = struct();

% 雷达参数
params.radar = struct();
params.radar.fc = 5.2e9;   % 载频 (Hz)
params.radar.B = 80e6;     % 带宽 (Hz)
params.radar.PRF = 1400;   % 脉冲重复频率 (Hz)

% 处理参数
params.processing = struct();
params.processing.range_bins = 'auto';  % 自动检测距离单元
params.processing.apply_window = true;  % 应用窗函数
params.processing.motion_comp = false;  % 不进行运动补偿
params.processing.dynamic_range_db = 30;  % 显示动态范围

% DCFT参数
params.dcft = struct();
params.dcft.alpha_step = 8;  % 啁啾率搜索步长
params.dcft.alpha_min = -16;  % 最小啁啾率
params.dcft.alpha_max = 320;  % 最大啁啾率
params.dcft.beta_step = 100;  % 啁啾率导数搜索步长
params.dcft.beta_min = -500;  % 最小啁啾率导数
params.dcft.beta_max = 2400;  % 最大啁啾率导数
params.dcft.thresholding = true;  % 启用阈值处理
params.dcft.threshold_ratio = 0.2;  % 阈值比例

% 3. 调用DCFT ISAR处理器
fprintf('\n开始DCFT ISAR处理...\n');
[ISAR_image, processing_info] = DCFT_ISAR_Processor(echo_data, params);

% 4. 保存结果
save('DCFT_ISAR_Results.mat', 'ISAR_image', 'processing_info');
fprintf('\n处理完成！结果已保存到 DCFT_ISAR_Results.mat\n');

% 5. 显示性能提升统计
fprintf('\n=== 性能提升统计 ===\n');
fprintf('对比度提升: %.2f%%\n', 100*(processing_info.contrast.dcft-processing_info.contrast.traditional)/processing_info.contrast.traditional);
fprintf('熵降低: %.2f%%\n', 100*(processing_info.entropy.traditional-processing_info.entropy.dcft)/processing_info.entropy.traditional);
fprintf('计算时间比: %.1fx\n', processing_info.time.dcft/processing_info.time.traditional); 