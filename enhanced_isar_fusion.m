% 增强型VMD-ADMM-DCFT融合ISAR成像算法
% 基于深度信号处理理论的高质量ISAR成像
% 解决散焦、旁瓣抑制和运动伪影问题

function enhanced_isar_fusion()
    clc; clear; close all;

    fprintf('=== 增强型VMD-ADMM-DCFT融合ISAR成像算法 ===\n');
    fprintf('正在初始化仿真参数...\n');

    % 1. 生成仿真数据
    [shipx2, radar_params] = generate_ship_data();

    % 2. 算法参数优化配置
    params = configure_enhanced_parameters();

    % 3. 传统FFT成像（基准）
    fprintf('执行传统FFT成像...\n');
    tic;
    traditional_image = fftshift(fft(shipx2, [], 2), 2);
    traditional_time = toc;

    % 4. 增强型VMD-ADMM-DCFT融合成像
    fprintf('执行增强型VMD-ADMM-DCFT融合成像...\n');
    tic;
    [enhanced_image, processing_info] = enhanced_vmd_admm_dcft_fusion(shipx2, params);
    enhanced_time = toc;

    % 5. 结果分析与可视化
    analyze_and_visualize_results(traditional_image, enhanced_image, ...
                                  traditional_time, enhanced_time, processing_info);
end

function [shipx2, radar_params] = generate_ship_data()
    % 舰船散射点模型
    Pos = [-10 -1 0;-9 -1 0;-8 -1 0;-7 -1 0;-6 -1 0;-5 -1 0;-3.75 -1 0;-3 -1 0;-2 -1 0;-1 -1 0;...
           0 -1 0;...
           1 -1 0;2 -1 0;3 -1 0;4 -1 0;5 -1 0;6 -1 0;7 -1 0;8 -1 0;9 -1 0;10 -1 0;...
           -9.5 0.2 0.5;...
           -9.5 1.2 0.5;-9 1 0;-8 1 0;-7 1 0;-6 1 0;-5.2 1.2 0;-4.1 1 0;-3 1 0;-2 1 0;-1 1 0;...
           0 1 0;...
           1 1 0;2 1 0;3 1 0;4 1 0;5 1 0;6 1 0;7 1 0;8 1 0;9 1 0;10 1 0;...
           10.5 -0.75 0;10.5 0.75 0;11 -0.5 0;11 0.5 0;11.5 0 0;...
           9.5 0.5 0.5;9.5 -0.5 0.5;9 0 0.5;8.5 0.5 0.5;8.5 -0.5 0.5;...
           5 0 0.5;5 0 1;5 0 1.5;5 0 2;5 0 2.5;5 0 3;5 0 3.5;5 0 4;...
           5.5 0.5 0.5;5.5 -0.5 0.5;4.5 0.5 0.5;4.5 -0.5 0.5;...
           0.5 0.5 0.9;0.5 -0.5 0.9;-0.5 0.5 0.9;-0.5 -0.5 0.9;0 0 0.5;...
           -5 0 0.2;-5 0.2 0.8;-5 0.2 1.4;-5 0 2;...
           -5.5 -0.5 0.5;-5.5 0.5 0.5;-4.4 0.5 0.5;-4.5 -0.6 0.5;...
           ];

    % 坐标变换
    x_Pos = Pos(:,1)*5;
    y_Pos = Pos(:,2)*5;
    z_Pos = Pos(:,3)*5;
    min_x_Pos = min(x_Pos);
    x_Pos = x_Pos - min_x_Pos;
    min_y_Pos = min(y_Pos);
    y_Pos = y_Pos - min_y_Pos;
    min_z_Pos = min(z_Pos);
    z_Pos = z_Pos - min_z_Pos;

    % 雷达视线方向
    R = [cos(3*pi/8)*cos(0), cos(3*pi/8)*sin(0), sin(3*pi/8)];
    Num_point = length(x_Pos);

    % 计算相对位置
    x_r = zeros(1, Num_point);
    y_r = zeros(1, Num_point);
    z_r = zeros(1, Num_point);
    for n_point = 1:Num_point
        x_r(n_point) = y_Pos(n_point)*R(3) - z_Pos(n_point)*R(2);
        y_r(n_point) = z_Pos(n_point)*R(1) - x_Pos(n_point)*R(3);
        z_r(n_point) = x_Pos(n_point)*R(2) - y_Pos(n_point)*R(1);
    end

    % 运动参数（增强的三维运动模型）
    x_omega = 0.05;  % 初始角速度
    y_omega = 0.2;
    z_omega = 0.05;

    x_lambda = 0.05; % 角加速度
    y_lambda = 0.1;
    z_lambda = 0.05;

    x_gamma = 0.05;  % 角加加速度
    y_gamma = 0.4;
    z_gamma = 0.05;

    % 计算运动参数
    f = zeros(1, Num_point);
    alpha = zeros(1, Num_point);
    beta = zeros(1, Num_point);
    for n_point = 1:Num_point
        f(n_point) = x_r(n_point)*x_omega + y_r(n_point)*y_omega + z_r(n_point)*z_omega;
        alpha(n_point) = x_r(n_point)*x_lambda + y_r(n_point)*y_lambda + z_r(n_point)*z_lambda;
        beta(n_point) = x_r(n_point)*x_gamma + y_r(n_point)*y_gamma + z_r(n_point)*z_gamma;
    end

    % 雷达参数
    B = 80*1e6;      % 带宽
    c = 3*1e8;       % 光速
    PRF = 1400;      % 脉冲重复频率
    fc = 5.2*1e9;    % 载频
    delta_r = c/(2*B);
    r = -50*delta_r:delta_r:50*delta_r;
    tm = 0:(1/PRF):0.501;
    Num_r = length(r);
    Num_tm = length(tm);
    ones_r = ones(1, Num_r);
    ones_tm = ones(1, Num_tm);

    % 生成回波信号
    s_r_tm = 0;
    for n_point = 1:Num_point
        Delta_R0(n_point) = x_Pos(n_point)*R(1) + y_Pos(n_point)*R(2) + z_Pos(n_point)*R(3);
        Delta_R = f(n_point).*tm + (1/2)*alpha(n_point).*tm.^2 + (1/6)*beta(n_point).*tm.^3 + Delta_R0(n_point);
        sita_Delta_R = 4*pi*(fc/c)*Delta_R;

        % 增强散射强度模型
        if n_point > 53 && n_point < 62
            scattering_strength = 1.3;
        elseif n_point == 48
            scattering_strength = 1.2;
        else
            scattering_strength = 1.0;
        end

        s_r_tm = s_r_tm + scattering_strength * sinc((2*B/c)*(r.'*ones_tm - ones_r.'*Delta_R)) .* exp(1j*ones_r.'*sita_Delta_R);
    end

    shipx2 = s_r_tm;

    % 保存雷达参数
    radar_params = struct();
    radar_params.B = B;
    radar_params.c = c;
    radar_params.PRF = PRF;
    radar_params.fc = fc;
    radar_params.Num_r = Num_r;
    radar_params.Num_tm = Num_tm;
    radar_params.tm = tm;
    radar_params.r = r;
end

function params = configure_enhanced_parameters()
    % 增强型算法参数配置
    params = struct();

    % VMD参数 - 优化的变分模态分解
    params.vmd.K = 5;                    % 增加模态数以更好分离信号成分
    params.vmd.alpha = 5000;             % 增大惩罚因子提高分离度
    params.vmd.tau = 0.25;               % 优化更新步长
    params.vmd.tol = 1e-8;               % 提高收敛精度
    params.vmd.max_iter = 800;           % 增加迭代次数确保收敛
    params.vmd.adaptive_K = true;        % 自适应模态数选择
    params.vmd.energy_threshold = 0.95;  % 能量阈值
    params.vmd.use_hilbert = true;       % 使用Hilbert变换增强

    % DCFT参数 - 离散立方傅里叶变换
    params.dcft.alpha_range = [-300, 300];
    params.dcft.alpha_step = 8;
    params.dcft.beta_range = [-1500, 1500];
    params.dcft.beta_step = 40;
    params.dcft.gamma_range = [-1200, 1200];
    params.dcft.gamma_step = 60;
    params.dcft.use_fine_search = true;
    params.dcft.fine_search_factor = 0.5;
    params.dcft.use_fourth_order = true;
    params.dcft.phase_unwrap = true;

    % ADMM参数 - 交替方向乘子法
    params.admm.rho = 1.5;
    params.admm.max_iter = 300;
    params.admm.tol = 1e-7;
    params.admm.dynamic_rho = true;
    params.admm.rho_update_factor = 1.1;
    params.admm.use_frequency_weights = true;
    params.admm.use_continuity_constraint = true;
    params.admm.use_sparsity_adaptive = true;

    % 融合参数
    params.fusion.vmd_weight = 0.35;
    params.fusion.dcft_weight = 0.35;
    params.fusion.admm_weight = 0.30;
    params.fusion.data_fidelity_weight = 0.7;
    params.fusion.sparsity_weight = 0.3;
    params.fusion.use_iterative_refinement = true;
    params.fusion.refinement_iterations = 3;

    % 性能优化参数
    params.performance.use_parallel = false;  % 避免并行问题
    params.performance.show_progress = true;
    params.performance.visualize_intermediate = true;
    params.performance.save_intermediate = true;
end

function [enhanced_image, processing_info] = enhanced_vmd_admm_dcft_fusion(radar_data, params)
    % 增强型VMD-ADMM-DCFT融合算法主函数

    [num_range_bins, num_azimuth] = size(radar_data);
    fprintf('处理数据尺寸: %d x %d\n', num_range_bins, num_azimuth);

    % 数据预处理
    radar_data = preprocess_radar_data(radar_data);

    % 初始化处理信息结构
    processing_info = initialize_processing_info(num_range_bins, num_azimuth);

    % 创建时间轴
    tm = (0:num_azimuth-1) / num_azimuth;

    % 初始化补偿后的信号
    s_compensated = zeros(size(radar_data), 'like', radar_data);

    % 主处理循环
    fprintf('开始增强型VMD-ADMM-DCFT处理...\n');
    for r_idx = 1:num_range_bins
        if params.performance.show_progress && mod(r_idx, 10) == 0
            fprintf('处理距离单元: %d/%d (%.1f%%)\n', r_idx, num_range_bins, 100*r_idx/num_range_bins);
        end

        % 获取当前距离单元信号
        signal = radar_data(r_idx, :);

        % 跳过低能量信号
        if sum(abs(signal).^2) < 1e-10
            s_compensated(r_idx, :) = signal;
            continue;
        end

        % 1. 增强型VMD分解
        [vmd_modes, vmd_freqs] = enhanced_vmd_decomposition(signal, params.vmd);

        % 2. DCFT相位估计
        [phase_estimates, phase_params] = enhanced_dcft_estimation(vmd_modes, tm, params.dcft);

        % 3. ADMM稀疏重建
        [reconstructed_signal] = enhanced_admm_reconstruction(signal, vmd_modes, phase_estimates, params.admm);

        % 4. 智能融合
        [compensated_signal] = intelligent_fusion(signal, vmd_modes, phase_estimates, reconstructed_signal, params.fusion);

        % 存储结果
        s_compensated(r_idx, :) = compensated_signal;

        % 保存中间结果用于分析
        if params.performance.save_intermediate
            processing_info.vmd_modes{r_idx} = vmd_modes;
            processing_info.phase_estimates{r_idx} = phase_estimates;
            processing_info.reconstructed_signals{r_idx} = reconstructed_signal;
        end
    end

    % 后处理增强
    s_compensated = post_processing_enhancement(s_compensated, params);

    % 方位向FFT成像
    enhanced_image = fftshift(fft(s_compensated, [], 2), 2);

    % 更新处理信息
    processing_info.compensated_data = s_compensated;
    processing_info.final_image = enhanced_image;

    fprintf('增强型VMD-ADMM-DCFT处理完成\n');
end

function radar_data = preprocess_radar_data(radar_data)
    % 数据预处理：去噪、归一化等

    % 1. 归一化
    max_val = max(abs(radar_data(:)));
    if max_val > 0
        radar_data = radar_data / max_val;
    end

    % 2. 去除直流分量
    for r_idx = 1:size(radar_data, 1)
        radar_data(r_idx, :) = radar_data(r_idx, :) - mean(radar_data(r_idx, :));
    end

    % 3. 预滤波（可选）
    % 使用低通滤波器去除高频噪声
    for r_idx = 1:size(radar_data, 1)
        signal = radar_data(r_idx, :);
        if sum(abs(signal).^2) > 1e-10
            % 简单的移动平均滤波
            window_size = 3;
            filtered_signal = conv(signal, ones(1, window_size)/window_size, 'same');
            radar_data(r_idx, :) = 0.8 * signal + 0.2 * filtered_signal;
        end
    end
end

function processing_info = initialize_processing_info(num_range_bins, num_azimuth)
    % 初始化处理信息结构
    processing_info = struct();
    processing_info.vmd_modes = cell(num_range_bins, 1);
    processing_info.phase_estimates = cell(num_range_bins, 1);
    processing_info.reconstructed_signals = cell(num_range_bins, 1);
    processing_info.quality_metrics = struct();
end

function [vmd_modes, vmd_freqs] = enhanced_vmd_decomposition(signal, vmd_params)
    % 增强型变分模态分解

    N = length(signal);
    signal = signal(:).';  % 确保为行向量

    % 自适应确定模态数
    if vmd_params.adaptive_K
        K_optimal = estimate_optimal_modes(signal, vmd_params);
    else
        K_optimal = vmd_params.K;
    end

    % 初始化
    alpha = vmd_params.alpha;
    tau = vmd_params.tau;
    tol = vmd_params.tol;
    max_iter = vmd_params.max_iter;

    % 频域信号
    signal_fft = fft(signal);
    freqs = (0:N-1)/N;

    % 初始化模态和中心频率
    u_k = zeros(K_optimal, N, 'like', 1j);
    omega_k = zeros(K_optimal, 1);
    lambda = zeros(1, N, 'like', 1j);

    % 初始化中心频率（均匀分布）
    for k = 1:K_optimal
        omega_k(k) = (k-0.5) * (0.5/K_optimal);
    end

    % VMD迭代
    for iter = 1:max_iter
        % 保存上一次迭代结果
        u_k_prev = u_k;
        omega_k_prev = omega_k;

        % 更新每个模态
        sum_uk = zeros(1, N, 'like', 1j);
        for k = 1:K_optimal
            % 计算除当前模态外的所有模态之和
            sum_u_i = sum_uk - u_k(k, :);

            % 在频域更新u_k
            num = signal_fft - fft(sum_u_i) + fft(lambda)/2;
            den = 1 + 2*alpha*(freqs - omega_k(k)).^2;

            u_k_fft = num ./ den;
            u_k(k, :) = ifft(u_k_fft);

            % 更新sum_uk
            sum_uk = sum_uk - u_k_prev(k, :) + u_k(k, :);

            % 更新中心频率
            power_spectrum = abs(u_k_fft).^2;
            if sum(power_spectrum) > 0
                omega_k(k) = sum(freqs .* power_spectrum) / sum(power_spectrum);
            end
        end

        % 更新拉格朗日乘子
        lambda = lambda + tau * (signal - sum_uk);

        % 检查收敛性
        u_diff = norm(u_k - u_k_prev, 'fro')^2 / (norm(u_k_prev, 'fro')^2 + eps);
        omega_diff = norm(omega_k - omega_k_prev)^2 / (norm(omega_k_prev)^2 + eps);

        if u_diff < tol && omega_diff < tol
            break;
        end
    end

    % 排序并归一化
    [omega_k, sort_idx] = sort(omega_k);
    u_k = u_k(sort_idx, :);

    % 能量归一化
    for k = 1:K_optimal
        mode_energy = sum(abs(u_k(k,:)).^2);
        if mode_energy > 0
            u_k(k,:) = u_k(k,:) / sqrt(mode_energy);
        end
    end

    vmd_modes = u_k;
    vmd_freqs = omega_k;
end

function K_optimal = estimate_optimal_modes(signal, vmd_params)
    % 估计最优模态数

    % 计算信号功率谱
    signal_fft = fft(signal);
    psd = abs(signal_fft).^2;

    % 找出主要峰值
    [~, locs] = findpeaks(psd, 'MinPeakHeight', 0.1*max(psd), 'MinPeakDistance', 5);

    % 基于峰值数量和能量分布确定最优模态数
    if isempty(locs)
        K_optimal = 2;
    else
        % 计算能量集中度
        sorted_psd = sort(psd, 'descend');
        cumsum_energy = cumsum(sorted_psd) / sum(sorted_psd);
        energy_threshold_idx = find(cumsum_energy > vmd_params.energy_threshold, 1);

        K_optimal = min(max(length(locs), 2), min(energy_threshold_idx/10, vmd_params.K));
        K_optimal = round(K_optimal);
    end

    K_optimal = max(2, min(K_optimal, vmd_params.K));
end

function [phase_estimates, phase_params] = enhanced_dcft_estimation(vmd_modes, tm, dcft_params)
    % 增强型DCFT相位估计

    [K, N] = size(vmd_modes);
    phase_estimates = zeros(K, N);
    phase_params = zeros(K, 4);  % [fd, alpha, beta, gamma]

    % 参数搜索范围
    alpha_range = dcft_params.alpha_range(1):dcft_params.alpha_step:dcft_params.alpha_range(2);
    beta_range = dcft_params.beta_range(1):dcft_params.beta_step:dcft_params.beta_range(2);

    if dcft_params.use_fourth_order
        gamma_range = dcft_params.gamma_range(1):dcft_params.gamma_step:dcft_params.gamma_range(2);
    else
        gamma_range = 0;
    end

    for k = 1:K
        mode_signal = vmd_modes(k, :);

        % 跳过低能量模态
        if sum(abs(mode_signal).^2) < 1e-12
            continue;
        end

        % 计算瞬时频率作为初始估计
        analytic_signal = hilbert(mode_signal);
        inst_phase = unwrap(angle(analytic_signal));
        inst_freq = diff([inst_phase(1), inst_phase]) / (2*pi);

        % 多项式拟合获得初始参数
        poly_coeffs = polyfit(tm, inst_freq, min(3, length(tm)-1));
        if length(poly_coeffs) >= 4
            fd_init = poly_coeffs(4);
            alpha_init = poly_coeffs(3) * 2;
            beta_init = poly_coeffs(2) * 6;
            gamma_init = poly_coeffs(1) * 24;
        else
            fd_init = 0;
            alpha_init = 0;
            beta_init = 0;
            gamma_init = 0;
        end

        % 精细搜索
        best_response = -inf;
        best_params = [fd_init, alpha_init, beta_init, gamma_init];

        % 构建搜索范围（以初始估计为中心）
        fd_search = fd_init + (-0.1:0.02:0.1);
        alpha_search = alpha_init + alpha_range(1:2:end);
        beta_search = beta_init + beta_range(1:2:end);

        if dcft_params.use_fourth_order
            gamma_search = gamma_init + gamma_range(1:3:end);
        else
            gamma_search = 0;
        end

        % 网格搜索
        for fd = fd_search
            for alpha = alpha_search
                for beta = beta_search
                    for gamma = gamma_search
                        % 构建去啁啾项
                        dechirp_phase = -1j * 2*pi * (fd*tm + (1/2)*alpha*tm.^2 + (1/6)*beta*tm.^3 + (1/24)*gamma*tm.^4);
                        dechirped = mode_signal .* exp(dechirp_phase);

                        % FFT处理并计算响应
                        spectrum = fft(dechirped);
                        response = max(abs(spectrum));

                        if response > best_response
                            best_response = response;
                            best_params = [fd, alpha, beta, gamma];
                        end
                    end
                end
            end
        end

        % 精细搜索（可选）
        if dcft_params.use_fine_search
            best_params = fine_search_dcft(mode_signal, tm, best_params, dcft_params);
        end

        % 构建最终相位模型
        fd = best_params(1);
        alpha = best_params(2);
        beta = best_params(3);
        gamma = best_params(4);

        phase_model = 2*pi * (fd*tm + (1/2)*alpha*tm.^2 + (1/6)*beta*tm.^3 + (1/24)*gamma*tm.^4);

        % 相位解缠绕
        if dcft_params.phase_unwrap
            phase_model = unwrap(phase_model);
        end

        phase_estimates(k, :) = phase_model;
        phase_params(k, :) = best_params;
    end
end

function best_params = fine_search_dcft(mode_signal, tm, initial_params, dcft_params)
    % DCFT参数精细搜索

    best_params = initial_params;
    best_response = -inf;

    % 精细搜索步长
    fine_factor = dcft_params.fine_search_factor;

    % 在初始参数周围进行精细搜索
    fd_fine = initial_params(1) + (-0.02:0.004:0.02);
    alpha_fine = initial_params(2) + (-dcft_params.alpha_step*fine_factor:dcft_params.alpha_step*fine_factor/5:dcft_params.alpha_step*fine_factor);
    beta_fine = initial_params(3) + (-dcft_params.beta_step*fine_factor:dcft_params.beta_step*fine_factor/5:dcft_params.beta_step*fine_factor);

    if dcft_params.use_fourth_order
        gamma_fine = initial_params(4) + (-dcft_params.gamma_step*fine_factor:dcft_params.gamma_step*fine_factor/5:dcft_params.gamma_step*fine_factor);
    else
        gamma_fine = 0;
    end

    for fd = fd_fine
        for alpha = alpha_fine
            for beta = beta_fine
                for gamma = gamma_fine
                    % 构建去啁啾项
                    dechirp_phase = -1j * 2*pi * (fd*tm + (1/2)*alpha*tm.^2 + (1/6)*beta*tm.^3 + (1/24)*gamma*tm.^4);
                    dechirped = mode_signal .* exp(dechirp_phase);

                    % FFT处理并计算响应
                    spectrum = fft(dechirped);
                    response = max(abs(spectrum));

                    if response > best_response
                        best_response = response;
                        best_params = [fd, alpha, beta, gamma];
                    end
                end
            end
        end
    end
end

function reconstructed_signal = enhanced_admm_reconstruction(original_signal, vmd_modes, phase_estimates, admm_params)
    % 增强型ADMM稀疏重建

    [K, N] = size(vmd_modes);

    % 构建补偿后的模态信号
    compensated_modes = zeros(K, N, 'like', 1j);
    for k = 1:K
        compensated_modes(k, :) = vmd_modes(k, :) .* exp(-1j * phase_estimates(k, :));
    end

    % 初始化ADMM变量
    X = fft(original_signal);  % 频域信号
    Z = X;                     % 辅助变量
    Lambda = zeros(size(X), 'like', 1j);  % 拉格朗日乘子

    % ADMM参数
    rho = admm_params.rho;
    max_iter = admm_params.max_iter;
    tol = admm_params.tol;

    % 构建目标频域信号
    Y_target = zeros(1, N, 'like', 1j);
    for k = 1:K
        Y_target = Y_target + fft(compensated_modes(k, :));
    end

    % 计算频率权重
    if admm_params.use_frequency_weights
        freq_weights = calculate_frequency_weights(original_signal, admm_params);
    else
        freq_weights = ones(1, N);
    end

    % 自适应稀疏性权重
    if admm_params.use_sparsity_adaptive
        sparsity_weight = calculate_adaptive_sparsity_weight(original_signal);
    else
        sparsity_weight = 0.1;
    end

    % ADMM迭代
    for iter = 1:max_iter
        X_prev = X;

        % 更新X（数据保真度项）
        X = (Y_target + rho * Z - Lambda) ./ (1 + rho);

        % 频率连续性约束
        if admm_params.use_continuity_constraint
            X_smooth = apply_continuity_constraint(X);
            X = 0.8 * X + 0.2 * X_smooth;
        end

        % 更新Z（稀疏性项）
        Z_update = X + Lambda/rho;
        Z = soft_threshold_weighted(Z_update, sparsity_weight/rho, freq_weights);

        % 更新Lambda（拉格朗日乘子）
        Lambda = Lambda + rho * (X - Z);

        % 动态调整rho
        if admm_params.dynamic_rho && mod(iter, 10) == 0
            rho = rho * admm_params.rho_update_factor;
        end

        % 收敛检查
        if norm(X - X_prev) / norm(X_prev) < tol
            break;
        end
    end

    % 重构时域信号
    reconstructed_signal = ifft(X);
end

function freq_weights = calculate_frequency_weights(signal, admm_params)
    % 计算频率权重

    signal_fft = fft(signal);
    psd = abs(signal_fft).^2;

    % 归一化功率谱密度
    psd_norm = psd / max(psd);

    % 计算权重（高能量频率分量获得更高权重）
    freq_weights = psd_norm + 0.1;  % 避免零权重
    freq_weights = freq_weights / max(freq_weights);
end

function sparsity_weight = calculate_adaptive_sparsity_weight(signal)
    % 计算自适应稀疏性权重

    signal_fft = fft(signal);
    sorted_mag = sort(abs(signal_fft), 'descend');
    cumsum_energy = cumsum(sorted_mag.^2) / sum(sorted_mag.^2);

    % 找到包含95%能量的频率分量数量
    sparsity_idx = find(cumsum_energy > 0.95, 1);
    sparsity_ratio = sparsity_idx / length(signal);

    % 根据稀疏度调整权重
    if sparsity_ratio < 0.1
        sparsity_weight = 0.05;  % 信号已经很稀疏
    elseif sparsity_ratio > 0.3
        sparsity_weight = 0.2;   % 信号不够稀疏，需要更强的稀疏约束
    else
        sparsity_weight = 0.1;   % 中等稀疏度
    end
end

function X_smooth = apply_continuity_constraint(X)
    % 应用频率连续性约束

    % 简单的移动平均滤波
    window_size = 3;
    X_smooth = conv(X, ones(1, window_size)/window_size, 'same');
end

function y = soft_threshold_weighted(x, lambda, weights)
    % 加权软阈值函数

    threshold = lambda ./ (weights + eps);
    y = sign(x) .* max(abs(x) - threshold, 0);
end

function compensated_signal = intelligent_fusion(original_signal, vmd_modes, phase_estimates, reconstructed_signal, fusion_params)
    % 智能融合算法

    [K, N] = size(vmd_modes);

    % 1. 基于VMD的补偿
    vmd_compensated = zeros(1, N, 'like', 1j);
    mode_energies = sum(abs(vmd_modes).^2, 2);
    [~, dominant_indices] = sort(mode_energies, 'descend');

    % 使用前几个主导模态进行补偿
    num_dominant = min(3, K);
    total_energy = sum(mode_energies(dominant_indices(1:num_dominant)));

    for i = 1:num_dominant
        k = dominant_indices(i);
        weight = mode_energies(k) / total_energy;
        compensated_mode = vmd_modes(k, :) .* exp(-1j * phase_estimates(k, :));
        vmd_compensated = vmd_compensated + weight * compensated_mode;
    end

    % 2. 基于DCFT的补偿
    dcft_compensated = original_signal;
    for k = 1:K
        if sum(abs(vmd_modes(k, :)).^2) > 1e-12
            dcft_compensated = dcft_compensated .* exp(-1j * phase_estimates(k, :) * mode_energies(k) / sum(mode_energies));
        end
    end

    % 3. 加权融合
    w1 = fusion_params.vmd_weight;
    w2 = fusion_params.dcft_weight;
    w3 = fusion_params.admm_weight;

    % 归一化权重
    total_weight = w1 + w2 + w3;
    w1 = w1 / total_weight;
    w2 = w2 / total_weight;
    w3 = w3 / total_weight;

    % 初始融合
    fused_signal = w1 * vmd_compensated + w2 * dcft_compensated + w3 * reconstructed_signal;

    % 4. 迭代优化（可选）
    if fusion_params.use_iterative_refinement
        for iter = 1:fusion_params.refinement_iterations
            % 计算残差
            residual = original_signal - fused_signal;

            % 基于残差调整权重
            residual_energy = sum(abs(residual).^2);
            if residual_energy > 1e-10
                % 增加数据保真度权重
                data_fidelity_factor = fusion_params.data_fidelity_weight;
                fused_signal = data_fidelity_factor * original_signal + (1 - data_fidelity_factor) * fused_signal;
            end
        end
    end

    compensated_signal = fused_signal;
end

function s_enhanced = post_processing_enhancement(s_compensated, params)
    % 后处理增强

    [num_range_bins, num_azimuth] = size(s_compensated);
    s_enhanced = s_compensated;

    % 1. 全局对比度增强
    for r_idx = 1:num_range_bins
        signal = s_enhanced(r_idx, :);
        if sum(abs(signal).^2) > 1e-10
            % 自适应对比度增强
            signal_mag = abs(signal);
            signal_phase = angle(signal);

            % 增强幅度对比度
            enhanced_mag = enhance_contrast(signal_mag);

            % 重构信号
            s_enhanced(r_idx, :) = enhanced_mag .* exp(1j * signal_phase);
        end
    end

    % 2. 边缘保持滤波
    s_enhanced = edge_preserving_filter(s_enhanced);

    % 3. 旁瓣抑制
    s_enhanced = sidelobe_suppression(s_enhanced);
end

function enhanced_mag = enhance_contrast(signal_mag)
    % 自适应对比度增强

    % 计算信号统计量
    mean_val = mean(signal_mag);
    std_val = std(signal_mag);

    if std_val > 0
        % 标准化
        normalized = (signal_mag - mean_val) / std_val;

        % 非线性增强
        enhanced = sign(normalized) .* (abs(normalized).^0.8);

        % 反标准化
        enhanced_mag = enhanced * std_val + mean_val;

        % 确保非负
        enhanced_mag = max(enhanced_mag, 0);
    else
        enhanced_mag = signal_mag;
    end
end

function s_filtered = edge_preserving_filter(s_compensated)
    % 边缘保持滤波

    [num_range_bins, num_azimuth] = size(s_compensated);
    s_filtered = s_compensated;

    % 使用双边滤波的思想
    for r_idx = 2:num_range_bins-1
        for a_idx = 2:num_azimuth-1
            % 获取邻域
            neighborhood = s_compensated(r_idx-1:r_idx+1, a_idx-1:a_idx+1);
            center_val = s_compensated(r_idx, a_idx);

            % 计算权重
            spatial_weights = [0.25, 0.5, 0.25; 0.5, 1.0, 0.5; 0.25, 0.5, 0.25];

            % 基于幅度差异的权重
            amplitude_diff = abs(abs(neighborhood) - abs(center_val));
            amplitude_weights = exp(-amplitude_diff / (0.1 * abs(center_val) + eps));

            % 组合权重
            combined_weights = spatial_weights .* amplitude_weights;
            combined_weights = combined_weights / sum(combined_weights(:));

            % 滤波
            s_filtered(r_idx, a_idx) = sum(sum(neighborhood .* combined_weights));
        end
    end
end

function s_suppressed = sidelobe_suppression(s_compensated)
    % 旁瓣抑制

    [num_range_bins, num_azimuth] = size(s_compensated);
    s_suppressed = s_compensated;

    % 对每个距离单元应用旁瓣抑制
    for r_idx = 1:num_range_bins
        signal = s_compensated(r_idx, :);

        if sum(abs(signal).^2) > 1e-10
            % 计算信号的FFT
            signal_fft = fft(signal);
            signal_mag = abs(signal_fft);
            signal_phase = angle(signal_fft);

            % 找到主峰
            [~, main_peak_idx] = max(signal_mag);

            % 计算旁瓣抑制权重
            freq_indices = 1:num_azimuth;
            distance_from_peak = abs(freq_indices - main_peak_idx);

            % 设计旁瓣抑制滤波器
            suppression_factor = ones(1, num_azimuth);
            sidelobe_threshold = 0.3 * signal_mag(main_peak_idx);

            for f_idx = 1:num_azimuth
                if distance_from_peak(f_idx) > 5 && signal_mag(f_idx) > sidelobe_threshold
                    % 抑制旁瓣
                    suppression_factor(f_idx) = 0.5;
                end
            end

            % 应用抑制
            suppressed_fft = signal_mag .* suppression_factor .* exp(1j * signal_phase);
            s_suppressed(r_idx, :) = ifft(suppressed_fft);
        end
    end
end

function analyze_and_visualize_results(traditional_image, enhanced_image, traditional_time, enhanced_time, processing_info)
    % 结果分析与可视化

    fprintf('\n=== 成像结果分析 ===\n');

    % 计算图像质量指标
    contrast_traditional = calculate_contrast(traditional_image);
    contrast_enhanced = calculate_contrast(enhanced_image);

    entropy_traditional = calculate_entropy(abs(traditional_image));
    entropy_enhanced = calculate_entropy(abs(enhanced_image));

    % 计算改进百分比
    contrast_improvement = 100 * (contrast_enhanced - contrast_traditional) / contrast_traditional;
    entropy_improvement = 100 * (entropy_traditional - entropy_enhanced) / entropy_traditional;

    % 打印结果
    fprintf('传统FFT方法:\n');
    fprintf('  对比度: %.4f\n', contrast_traditional);
    fprintf('  熵: %.4f\n', entropy_traditional);
    fprintf('  处理时间: %.2f秒\n', traditional_time);

    fprintf('\n增强型VMD-ADMM-DCFT融合方法:\n');
    fprintf('  对比度: %.4f (改进 %.1f%%)\n', contrast_enhanced, contrast_improvement);
    fprintf('  熵: %.4f (改进 %.1f%%)\n', entropy_enhanced, entropy_improvement);
    fprintf('  处理时间: %.2f秒\n', enhanced_time);

    % 可视化结果
    visualize_comparison_results(traditional_image, enhanced_image, processing_info);

    % 显示中间处理步骤
    if isfield(processing_info, 'vmd_modes') && ~isempty(processing_info.vmd_modes)
        visualize_processing_steps(processing_info);
    end
end

function contrast_val = calculate_contrast(image)
    % 计算图像对比度

    mag = abs(image);
    contrast_val = std(mag(:)) / (mean(mag(:)) + eps);
end

function entropy_val = calculate_entropy(image)
    % 计算图像熵

    % 归一化
    normalized = image / (sum(image(:)) + eps);
    normalized(normalized <= 0) = eps;

    % 计算熵
    entropy_val = -sum(normalized(:) .* log2(normalized(:)));
end

function visualize_comparison_results(traditional_image, enhanced_image, processing_info)
    % 可视化对比结果

    figure('Name', 'ISAR成像结果对比', 'Position', [100, 100, 1200, 500]);

    % 传统FFT结果
    subplot(1, 2, 1);
    traditional_db = 20*log10(abs(traditional_image) / max(abs(traditional_image(:))));
    imagesc(traditional_db);
    caxis([-40, 0]);
    colormap('jet');
    colorbar;
    title('传统FFT成像结果');
    xlabel('方位单元');
    ylabel('距离单元');
    axis xy;

    % 增强型融合结果
    subplot(1, 2, 2);
    enhanced_db = 20*log10(abs(enhanced_image) / max(abs(enhanced_image(:))));
    imagesc(enhanced_db);
    caxis([-40, 0]);
    colormap('jet');
    colorbar;
    title('增强型VMD-ADMM-DCFT融合结果');
    xlabel('方位单元');
    ylabel('距离单元');
    axis xy;

    % 保存图像
    saveas(gcf, 'ISAR_comparison_results.png');
    fprintf('对比结果已保存为: ISAR_comparison_results.png\n');
end

function visualize_processing_steps(processing_info)
    % 可视化中间处理步骤

    fprintf('可视化中间处理步骤...\n');

    % 选择中间距离单元进行分析
    num_range_bins = length(processing_info.vmd_modes);
    middle_range = floor(num_range_bins / 2);

    % 确保选择的距离单元有有效数据
    valid_range = middle_range;
    for r_idx = middle_range:min(middle_range+10, num_range_bins)
        if ~isempty(processing_info.vmd_modes{r_idx})
            valid_range = r_idx;
            break;
        end
    end

    if isempty(processing_info.vmd_modes{valid_range})
        fprintf('警告: 未找到有效的中间处理数据\n');
        return;
    end

    % 创建图形窗口
    figure('Name', '增强型VMD-ADMM-DCFT中间处理步骤', 'Position', [100, 100, 1400, 900]);

    % 1. VMD模态分解结果
    subplot(3, 3, 1);
    vmd_modes = processing_info.vmd_modes{valid_range};
    if ~isempty(vmd_modes)
        hold on;
        for k = 1:size(vmd_modes, 1)
            plot(abs(vmd_modes(k, :)), 'LineWidth', 1.5);
        end
        hold off;
        title(sprintf('VMD模态分解 (距离单元 %d)', valid_range));
        xlabel('方位单元');
        ylabel('幅度');
        legend(arrayfun(@(x) sprintf('模态 %d', x), 1:size(vmd_modes, 1), 'UniformOutput', false));
        grid on;
    end

    % 2. 相位估计结果
    subplot(3, 3, 2);
    phase_estimates = processing_info.phase_estimates{valid_range};
    if ~isempty(phase_estimates)
        hold on;
        for k = 1:size(phase_estimates, 1)
            plot(unwrap(phase_estimates(k, :)), 'LineWidth', 1.5);
        end
        hold off;
        title('DCFT相位估计');
        xlabel('方位单元');
        ylabel('相位 (rad)');
        legend(arrayfun(@(x) sprintf('模态 %d', x), 1:size(phase_estimates, 1), 'UniformOutput', false));
        grid on;
    end

    % 3. 原始信号频谱
    subplot(3, 3, 3);
    if isfield(processing_info, 'compensated_data')
        original_signal = processing_info.compensated_data(valid_range, :);
        original_fft = fftshift(fft(original_signal));
        freq_axis = linspace(-0.5, 0.5, length(original_fft));
        plot(freq_axis, 20*log10(abs(original_fft) / max(abs(original_fft))));
        title('补偿后信号频谱');
        xlabel('归一化频率');
        ylabel('幅度 (dB)');
        grid on;
        ylim([-60, 0]);
    end

    % 4. VMD模态能量分布
    subplot(3, 3, 4);
    if ~isempty(vmd_modes)
        mode_energies = sum(abs(vmd_modes).^2, 2);
        bar(mode_energies);
        title('VMD模态能量分布');
        xlabel('模态索引');
        ylabel('能量');
        grid on;
    end

    % 5. 相位补偿前后对比
    subplot(3, 3, 5);
    if ~isempty(vmd_modes) && ~isempty(phase_estimates)
        % 选择主导模态
        mode_energies = sum(abs(vmd_modes).^2, 2);
        [~, dominant_idx] = max(mode_energies);

        original_phase = angle(vmd_modes(dominant_idx, :));
        compensated_phase = original_phase - phase_estimates(dominant_idx, :);

        hold on;
        plot(unwrap(original_phase), 'b-', 'LineWidth', 1.5, 'DisplayName', '原始相位');
        plot(unwrap(compensated_phase), 'r-', 'LineWidth', 1.5, 'DisplayName', '补偿后相位');
        hold off;
        title('相位补偿前后对比');
        xlabel('方位单元');
        ylabel('相位 (rad)');
        legend;
        grid on;
    end

    % 6. 重构信号对比
    subplot(3, 3, 6);
    if isfield(processing_info, 'reconstructed_signals') && ~isempty(processing_info.reconstructed_signals{valid_range})
        original_signal = processing_info.compensated_data(valid_range, :);
        reconstructed_signal = processing_info.reconstructed_signals{valid_range};

        hold on;
        plot(abs(original_signal), 'b-', 'LineWidth', 1.5, 'DisplayName', '原始信号');
        plot(abs(reconstructed_signal), 'r--', 'LineWidth', 1.5, 'DisplayName', 'ADMM重构');
        hold off;
        title('信号重构对比');
        xlabel('方位单元');
        ylabel('幅度');
        legend;
        grid on;
    end

    % 7. 最终成像结果（局部）
    subplot(3, 3, 7);
    if isfield(processing_info, 'final_image')
        final_image = processing_info.final_image;
        final_db = 20*log10(abs(final_image) / max(abs(final_image(:))));

        % 显示局部区域
        range_start = max(1, valid_range - 10);
        range_end = min(size(final_image, 1), valid_range + 10);

        imagesc(final_db(range_start:range_end, :));
        caxis([-40, 0]);
        colormap('jet');
        colorbar;
        title('最终成像结果 (局部)');
        xlabel('方位单元');
        ylabel('距离单元');
        axis xy;
    end

    % 8. 质量指标随距离单元变化
    subplot(3, 3, 8);
    if isfield(processing_info, 'final_image')
        final_image = processing_info.final_image;
        contrast_per_range = zeros(size(final_image, 1), 1);

        for r_idx = 1:size(final_image, 1)
            signal_mag = abs(final_image(r_idx, :));
            if sum(signal_mag) > 0
                contrast_per_range(r_idx) = std(signal_mag) / mean(signal_mag);
            end
        end

        plot(contrast_per_range, 'LineWidth', 2);
        title('对比度随距离单元变化');
        xlabel('距离单元');
        ylabel('对比度');
        grid on;

        % 标记当前分析的距离单元
        hold on;
        plot(valid_range, contrast_per_range(valid_range), 'ro', 'MarkerSize', 8, 'MarkerFaceColor', 'r');
        hold off;
    end

    % 9. 算法性能总结
    subplot(3, 3, 9);
    axis off;

    % 显示算法参数和性能指标
    text_str = {
        '算法参数总结:',
        sprintf('VMD模态数: 自适应 (最大%d)', 5),
        sprintf('DCFT搜索范围: α[%d,%d], β[%d,%d]', -300, 300, -1500, 1500),
        sprintf('ADMM迭代次数: %d', 300),
        '',
        '处理特点:',
        '• 自适应模态数选择',
        '• 四阶相位估计',
        '• 频率权重ADMM重建',
        '• 智能融合策略',
        '• 旁瓣抑制后处理'
    };

    text(0.05, 0.95, text_str, 'Units', 'normalized', 'VerticalAlignment', 'top', ...
         'FontSize', 10, 'FontWeight', 'bold');

    % 保存中间步骤图像
    saveas(gcf, 'ISAR_processing_steps.png');
    fprintf('中间处理步骤已保存为: ISAR_processing_steps.png\n');
end
