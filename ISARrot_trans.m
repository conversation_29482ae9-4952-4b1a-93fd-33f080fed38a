%-------------------------------------------------------------------------%
%--------        Range-azimuth image after range compressrion      -------%
%--------                 wuli<PERSON>(20101126-2010)                   -------%
%-------------------------------------------------------------------------%
clc; clear all;%close all;

%-----------------------------����ɢ���ģ�� ------------------------------%
Pos = [-10 -1 0;-9 -1 0;-8 -1 0;-7 -1 0;-6 -1 0;-5 -1 0;-3.75 -1 0;-3 -1 0;-2 -1 0;-1 -1 0;...   %(10��) 
       0 -1 0;...      %(1��)
       1 -1 0;2 -1 0;3 -1 0;4 -1 0;5 -1 0;6 -1 0;7 -1 0;8 -1 0;9 -1 0;10 -1 0;...  %(10��)
       -9.5 0.2 0.5;...    %(1��)
       -9.5 1.2 0.5;-9 1 0;-8 1 0;-7 1 0;-6 1 0;-5.2 1.2 0;-4.1 1 0;-3 1 0;-2 1 0;-1 1 0;...  %(10��)
       0 1 0;...      %(1��)
       1 1 0;2 1 0;3 1 0;4 1 0;5 1 0;6 1 0;7 1 0;8 1 0;9 1 0;10 1 0;...     %(10��)
       10.5 -0.75 0;10.5 0.75 0;11 -0.5 0;11 0.5 0;11.5 0 0;... %�ױ�        %(5��)
       9.5 0.5 0.5;9.5 -0.5 0.5;9 0 0.5;8.5 0.5 0.5;8.5 -0.5 0.5;... %��ͷ��   %(5��) 9.5 0 0.5;9 0.5 0.5;9 -0.5 0.5;8.5 0 0.5;
       5 0 0.5;5 0 1;5 0 1.5;5 0 2;5 0 2.5;5 0 3;5 0 3.5;5 0 4;...   %��ͷ��       %(8��)
       5.5 0.5 0.5;5.5 -0.5 0.5;4.5 0.5 0.5;4.5 -0.5 0.5;... %��ͷ�˶�      %(4��)5 0 0.5; 5.5 0 0.5;5 0.5 0.5;5 -0.5 0.5; 4.5 0 0.5;  
       0.5 0.5 0.9;0.5 -0.5 0.9;-0.5 0.5 0.9;-0.5 -0.5 0.9;0 0 0.5;... %���ж�   %(5��) 0.5 0 1;-0.5 0 1;0 0.5 1;0 -0.5 1;
       -5 0 0.2;-5 0.2 0.8;-5 0.2 1.4;-5 0 2;... %��β��    %(4��) 
       -5.5 -0.5 0.5;-5.5 0.5 0.5;-4.4 0.5 0.5;-4.5 -0.6 0.5;...��β�˶�  %(4��)-5 0 0.5;-5.5 0 0.5; -5 0.5 0.5;-5 -0.5 0.5;-4.5 0 0.5;
       ];
% x_Pos2 = Pos2(:,1)*5;
% y_Pos2 = Pos2(:,2)*5;
% z_Pos2 = Pos2(:,3)*5;
% min_x_Pos = min(x_Pos2);
% min_y_Pos = min(y_Pos2);
% min_z_Pos = min(z_Pos2);
%----------for test---------%
% Pos = [10 -1 0;10 1 0;10.5 -0.75 0;10.5 0.75 0;9.5 0.5 0.5;9.5 -0.5 0.5;9 0 0.5;8.5 0.5 0.5;8.5 -0.5 0.5];   %
%---------------------------%
%-------------------------------------------------------------------------%

%-------------------------------    ��ʾ   -------------------------------%
x_Pos = Pos(:,1)*5;
y_Pos = Pos(:,2)*5;
z_Pos = Pos(:,3)*5;
min_x_Pos = min(x_Pos);
x_Pos = x_Pos + min_x_Pos;
min_y_Pos = min(y_Pos);
y_Pos = y_Pos + min_y_Pos;
min_z_Pos = min(z_Pos);
z_Pos = z_Pos + min_z_Pos;
figure
plot3(x_Pos,y_Pos,z_Pos,'*')
grid on



R = [cos(3*pi/8)*cos(0),cos(3*pi/8)*sin(0),sin(3*pi/8)];  %�״�Ŀ�����ߵ�λʸ��
Num_point = length(x_Pos); %Ŀ�����
x_r = zeros(1,Num_point);
y_r = zeros(1,Num_point);
z_r = zeros(1,Num_point);
for n_point = 1:Num_point
    x_r(n_point) = y_Pos(n_point)*R(3)-z_Pos(n_point)*R(2);
    y_r(n_point) = z_Pos(n_point)*R(1)-x_Pos(n_point)*R(3);
    z_r(n_point) = x_Pos(n_point)*R(2)-y_Pos(n_point)*R(1);
end

x_oumiga = 0.05; %Ŀ����ת��ʼ���ٶ�
y_oumiga = 0.2; %
z_oumiga = 0.05;

x_lamda = 0.05; %0.05%Ŀ����ת���ٶȼ��ٶ�
y_lamda = 0.1; %0.1
z_lamda = 0.05; %0.05

x_gamma = 0.05; %0.05%Ŀ����ת���ٶȼӼ��ٶ�
y_gamma = 0.4; %0.4
z_gamma = 0.05; %0.05

f = zeros(1,Num_point);
alpha = zeros(1,Num_point);
beita = zeros(1,Num_point);
for n_point = 1:Num_point
    f(n_point) = x_r(n_point)*x_oumiga+y_r(n_point)*y_oumiga+z_r(n_point)*z_oumiga;
    alpha(n_point) = x_r(n_point)*x_lamda+y_r(n_point)*y_lamda+z_r(n_point)*z_lamda;
    beita(n_point) = x_r(n_point)*x_gamma+y_r(n_point)*y_gamma+z_r(n_point)*z_gamma;
end

%---------------------------�״������ʱ����ز�---------------------------%
B = 80*1e6;  %����
c = 3*1e8; 
PRF = 1400; %�����ظ�Ƶ��
fc = 5.2*1e9; %��Ƶ
delta_r = c/(2*B);
r = -50*delta_r:delta_r:50*delta_r;
tm = 0:(1/PRF):0.501;
Num_r = length(r);
Num_tm = length(tm);
ones_r = ones(1,Num_r);
ones_tm = ones(1,Num_tm);

s_r_tm = 0; %Խ����У����
for n_point = 1:Num_point
    Delta_R0(n_point) = x_Pos(n_point)*R(1)+y_Pos(n_point)*R(2)+z_Pos(n_point)*R(3);%��ʼʱ�̵ľ�����
    Delta_R = f(n_point).*0+(1/2)*alpha(n_point).*0.*0+(1/6)*beita(n_point).*0.*0.*tm + Delta_R0(n_point);
    sita_Delta_R = 4*pi*(fc/c)*((f(n_point)+0*c/(2*fc)).*tm+(1/2)*alpha(n_point).*0.*0+(1/6)*beita(n_point).*0.*0.*0 + Delta_R0(n_point)); %+4.5����Ŀ����ͼ����λ��
    s_r_tm = s_r_tm + sinc((2*B/c)*(r.'*ones_tm-ones_r.'*Delta_R)).*exp(j*ones_r.'*sita_Delta_R);
end
% figure
% imagesc(flipud(abs(fft(s_r_tm,Num_tm,2))))  %ʵ��ʹ�õĳ�������PRF = 1400; 190--370

tm2 = 0:(1/PRF):0.501;
Num_tm2 = length(tm2);
ones_tm2 = ones(1,Num_tm2);
s_r_tm2 = 0;  %Խ����У����
for n_point = 1:Num_point
    Delta_R0(n_point) = x_Pos(n_point)*R(1)+y_Pos(n_point)*R(2)+z_Pos(n_point)*R(3);%��ʼʱ�̵ľ�����
    Delta_R = f(n_point).*0+(1/2)*alpha(n_point).*0.*tm2+(1/6)*beita(n_point).*0.*tm2.*tm2 + Delta_R0(n_point);
    sita_Delta_R = 4*pi*(fc/c)*((f(n_point)).*tm2+(1/2)*alpha(n_point).*tm2.*tm2+(1/6)*beita(n_point).*tm2.*tm2.*tm2 + Delta_R0(n_point)); %+4.5����Ŀ����ͼ����λ��
    if n_point>53 & n_point<62
        s_r_tm2 = s_r_tm2 + 1.3*sinc((2*B/c)*(r.'*ones_tm2-ones_r.'*Delta_R)).*exp(j*ones_r.'*sita_Delta_R);
    else
        s_r_tm2 = s_r_tm2 + sinc((2*B/c)*(r.'*ones_tm2-ones_r.'*Delta_R)).*exp(j*ones_r.'*sita_Delta_R);        
    end
    
    if n_point == 48
        s_r_tm2 = s_r_tm2 + 1*sinc((2*B/c)*(r.'*ones_tm2-ones_r.'*Delta_R)).*exp(j*ones_r.'*sita_Delta_R);
    else
        s_r_tm2 = s_r_tm2 + sinc((2*B/c)*(r.'*ones_tm2-ones_r.'*Delta_R)).*exp(j*ones_r.'*sita_Delta_R);
    end
end
% %---------------------------- ����ƽ�Ƶ��� --------------------------------%
% s_phase_comp1 = exp(j*ones_r.'*(2*pi*((1/2)*40.*tm2.*tm2+(1/6)*400.*tm2.*tm2.*tm2)));  %��������λ����ֹ���ָ�ֵ��ʵ�ʳ�����
% % s_phase_comp =exp(j*ones_r.'*(2*pi*(190.*tm2+(1/2)*40.*tm2.*tm2+(1/6)*400.*tm2.*tm2.*tm2)));  %��������λ����ֹ���ָ�ֵ��ʵ�ʳ�����
% s_phase_comp =exp(j*ones_r.'*(2*pi*(190.*tm2))); %ֻ�г���Doppler������
% s_r_tm2 = s_r_tm2.*s_phase_comp; %
figure
imagesc(flipud(abs(fft(s_r_tm2,Num_tm,2))));
%-------------------------------------------------------------------------%

%-----------------------------    ����      -----------------------------%
Mu = 0;
Sigmma = sqrt(10.^(1.5));
n_t_tm = normrnd(Mu,Sigmma,Num_r,Num_tm2); %ϵ��(1/length(r_profile))ΪIDFT���
n_r_t = fft(n_t_tm,Num_r)./Num_r;
% s_r_tm2 = s_r_tm2 + n_r_t; %�൱��ԭʼ�ز���������
%-------------------------------------------------------------------------%

%-----------------���ÿһ��ɢ����Ƶ�ʣ���Ƶ�ʼ���Ƶ�ʱ仯��---------------%
% [r_max,n_r_max]=max(abs(fft(s_r_tm.')));

%------------------------------------------------------------------------%
 %s_r_tm2(35,:) = 0;
%------------------------  ������Ϊ����ʱ�ĳ�����  ---------------------%
% R_D = abs((fft(s_r_tm2,Num_tm,2)));
% R_D(1:2,:) = [];
% R_D(Num_r-1-2:Num_r-2,:) = [];
% R_D = horzcat(R_D(:,Num_tm2-79:Num_tm2),R_D(:,1:Num_tm2-80));
% %%%%Ϊ�˷������·���չʾ�������ĳ�����
% R_D(51,:) = 1.2*R_D(51,:);
% R_D(51,160:210) = 0.4*R_D(51,160:210); %Ϊ�˺�MDCFT�Ա�
% R_D(43,:) = 0.6*R_D(43,:);
% R_D(43,325:355) = 0.4*R_D(43,325:355);
% 
% % figure
% % imagesc(flipud(abs(s_r_tm2)))
% R_D_max = max(max(R_D));
% R_D = R_D_max-R_D;
% figure
% imagesc(flipud(R_D./R_D_max))
% %colormap('gray')
% %colorbar('EastOutside',{'1','0.9','0.8','0.7','0.6','0.5','0.4','0.3','0.2','0.1','0'})

Y=fft(s_r_tm2,Num_tm,2); %直接FFT
G1=20*log10(abs(Y)./max(abs(Y(:))));
figure('name','DCT成像结果');
imagesc(G1);caxis([-30,0]);
grid on;axis xy;colorbar;%axis equal;
% axis([-0.6 0.6 y(select_row(1)) y(select_row(end))]);%set(gca,'xtick',[-0.2 0 0.2]);
xlabel('azimuth');ylabel('range (m)');colormap jet;

 X1=s_r_tm2;%%D901

%X1=X;
% [na,nr] = size(X1); 
% nstd    = .505*0.03*sqrt(2);    %1.57 
% noise = random('normal',0,nstd,na,nr) + 1j*random('normal',0,nstd,na,nr) ;
% SNR = db(norm(y,'fro')^2/(var(noise(:))*na*nr))/2;
% fprintf('SNR=%d;\n',SNR)
% X1=X1+noise;
% 
% [N,M] = size(X1);
% [na,nr] = size(X1);     %
% % X1=X(1:2:64,1:32);
% % X1=X(1:4:64,449:512);
% % f0=9e9;
% % c=3e8;
% % B=0.512e9;
% % %X1 = Keystone(X1,B,f0);
% WN=4;
% [m,n]=size(X1);
% w=hammwin(WN);%%Hamming Window is better for this.
% 
% Xf=fft(X1,[],2);%% Take FFT along Rowwise (2).
% [row, len]=size(Xf);
% 
% xtemp=Xf;
% Xf=zeros(len,len+WN);%% Pad zeros to the start as well as end, to reduce the effect
% Xf(1:row,WN/2+1:len+WN/2)=xtemp;%% of neglecting last window length points.
% [row, len]=size(Xf);
% 
% [row, len] = size(Xf);
% Xf = Xf(:, (WN/2 + 1):(len - WN/2)); % Remove the padded zeros before processing
% [row, len] = size(Xf);
% 
% for i1=1:m
%     xf=Xf(i1,:);
%     wx=isar_hwtmapf(xf,w,len);
%     V((i1-1)*len+1:i1*len, :) = wx;
%     i1
% end
% 
% V=fftshift(V,2);
% 
% [m,n]=size(V);
% m2 = m/n;
% m1 = n;
% 
% % figure
% for i1=1:1:n
%     for k1=1:1:m2
%         temp(k1,:)=V(i1+(k1-1)*m1,:);
%     end           
%     t3 = temp;
% 
% end
% temp=fftshift(temp,2);
% 
% % 
% figure('name','ISAR');
% G1=20*log10(abs(temp)/max(abs(temp(:))));
% imagesc(G1);caxis([-55 0]);
% %axis equal;axis([-0.6 0.6 -0.6 0.6]);
% xlabel('Azimuth cells');ylabel('Range cells');
% grid on;axis xy;%set(gca,'XDir','reverse');
% % set(gca,'xtick',[-0.6 -0.4 -0.2 0 0.2 0.4 0.6]);
% % set(gca,'ytick',[-0.6 -0.4 -0.2 0 0.2 0.4 0.6]);
% colorbar;colormap jet;
% e=EntropyImage(t3+eps);
% c=contrast(t3);
% function z = isar_hwtmapf1(xf, w, len)
%     k = length(w);
%     half_k = floor(k / 2);
%     z = zeros(len, len);
% 
%     % 添加自适应参数
%     alpha = 0.01;  % 自适应步长
%     beta = 0.5;    % 正则化参数
% 
%     % 对每个时间点构建自适应谐波小波变换
%     for ii = 1:len
%         % 获取当前时间窗口的信号
%         start_idx = max(1, ii - half_k);
%         end_idx = min(len, ii + half_k - 1);
%         curr_sig = zeros(1, len);
%         curr_sig(start_idx:end_idx) = xf(start_idx:end_idx);
% 
%         % 计算信号能量
%         signal_energy = sum(abs(curr_sig(start_idx:end_idx)).^2);
% 
%         % 构建自适应基矩阵
%         H = zeros(len, end_idx-start_idx+1);
%         for m = 1:len
%             for n = 1:(end_idx-start_idx+1)
%                 t = start_idx + n - 2;
%                 f = m - 1;
% 
%                 % 计算自适应权重
%                 weight = 1 / (1 + beta * exp(-alpha * signal_energy));
% 
%                 % 增强的基函数
%                 phase = -2*pi*f*t/len;
%                 H(m,n) = weight * w(n) * exp(1i*phase);
% 
%                 % 添加频率调制项
%                 freq_mod = exp(-alpha * abs(f-len/2)/len);
%                 H(m,n) = H(m,n) * freq_mod;
%             end
%         end
% 
%         % 正则化基矩阵
%         H = H ./ (norm(H, 'fro') + eps);
% 
%         % 应用自适应谐波小波变换
%         windowed_sig = curr_sig(start_idx:end_idx);
% 
%         % 添加噪声抑制
%         noise_threshold = 0.1 * max(abs(windowed_sig));
%         windowed_sig(abs(windowed_sig) < noise_threshold) = 0;
% 
%         % 计算变换系数
%         temp = H * windowed_sig.';
% 
%         % 非线性增强
%         z(:,ii) = abs(temp).^2 .* (1 + tanh(abs(temp)));
%     end
% 
% 
% end
% 
% 
% 
%  S=s_r_tm2;
% [nr,np] = size(S);
% %Xr = fftshift(fft(S),1);
% 
% g = hamming(oddnumber(np/10));
% h = hamming(oddnumber(np/4));
% for k=1:nr
%    fprintf('range cell %g/%g\n',k,nr)
%    s = S(k,:).';
%    TFT = tfrspwv(s,1:np,np,g,h);
%    Y(k,1:np,1:16) = abs(fftshift(TFT(:,1:np/16:np),1));
% end
% 
% figure
% colormap(jet)
% %Im=fftshift(fft2(S));
% Im=iftx(ifty(S));
% figure('name','ISAR');
% G2=20*log10(abs(Im))/max(abs(Im(:)));
% imagesc(G2);
% imagesc(20*log10(abs(fftshift(fft2(S)))));
% axis xy
% xlabel('Doppler cells')
% ylabel('range cells')
% title('FFT image')
% clim = get(gca,'CLim');
% set(gca,'CLim',clim(2) + [-20 0]);
% drawnow
% 
% for k=1:16%%%%%%ya  4 D901 7 8
%      G = zeros(nr,2*np);
%      figure(k+1)
%      xc = floor(centro1d(sum(Y(:,:,k))))-np/2;
%      if xc >= 0
%          G(:,np/2+1:np/2+np-xc) = Y(:,1+xc:np,k);
%          G(:,np/2+np-xc+1:np/2+np) = Y(:,1:xc,k);
%      else
%          G(:,np/2+1+abs(xc):np/2+np) = Y(:,1:np-abs(xc),k);
%          G(:,np/2+1:np/2+1+abs(xc)) = Y(:,np-abs(xc):np,k);
%      end
%      IE(k) = EntropyImage( G+eps )
%      %G3=20*log10(abs(G))/max(abs(G(:)));
%      imagesc(G);
%      xlabel('Azimuth Cell');ylabel('Range cell');
%     grid on;axis xy;%set(gca,'XDir','reverse');
%     % set(gca,'xtick',[-0.6 -0.4 -0.2 0 0.2 0.4 0.6]);
%     % set(gca,'ytick',[-0.6 -0.4 -0.2 0 0.2 0.4 0.6]);
%     colorbar;
% 
%      axis xy
%      xlabel('Doppler cells')
%      ylabel('Range cells')
%      title('SPWV Range-Doppler Image')
% %      clim = get(gca,'CLim');
% %      set(gca,'CLim',clim(2) + [-20 0]);
%      drawnow
% end
% %G=G(:,129:384);
% figure('name','ISAR');
% G1=20*log10(abs(G)/max(abs(G(:))));
% imagesc(G1);caxis([-30 0]);
% xlabel('Azimuth cells');ylabel('Range cells');
% grid on;axis xy;colormap jet;
% colorbar;
% 
% 
% 
% % G=G(:,129:384)
% % figure('name','ISAR');
% % G1=20*log10(abs(G)/max(abs(G(:))));
% % imagesc(G1);caxis([-60 0]);
% % %axis equal;axis([-0.6 0.6 -0.6 0.6]);
% % xlabel('Azimuth cells');ylabel('Range cells');
% % grid on;axis xy;%set(gca,'XDir','reverse');
% % % set(gca,'xtick',[-0.6 -0.4 -0.2 0 0.2 0.4 0.6]);
% % % set(gca,'ytick',[-0.6 -0.4 -0.2 0 0.2 0.4 0.6]);
% % colorbar;ftresize(13);
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
% % --- User's provided code (first part) ---
% % ... (Pos, x_Pos, y_Pos, z_Pos, R, Num_point, etc.) ...
% % ... (B, c, PRF, fc, delta_r, r, tm, Num_r, Num_tm) ...
% % ... (s_r_tm definition - this is the one for Fig 4a in paper, not directly used for CVMD) ...
% % ... (tm2, Num_tm2, s_r_tm2 definition - THIS IS THE INPUT ECHO) ...
% % ... (s_phase_comp, and s_r_tm2 = s_r_tm2.*s_phase_comp; ) ...
% 
% % --- Parameters for ICVMD (as per paper or adapted) ---
% K = 3; % 模式数量 (Number of modes)
% alpha_cvmd = 2000; % 平衡参数 (Balancing parameter)
% tau_cvmd = 0; % 双上升时间步长 (Time-step of dual ascent for lambda initialization, actual update might use a default or be embedded)
%               % The paper's (16) has tau. If not specified, a small positive value like 0.1 or 1 could be tried for lambda update.
%               % Or, it might be that lambda is updated without an explicit tau, relying on the residual.
%               % Let's re-check eq (16): Lambda_new = Lambda_old + tau * residual.
%               % A common choice for tau in ADMM is related to alpha or can be tuned.
%               % For now, let's assume it's implicitly handled or a default is fine.
%               % The paper's text says "lambda_i^1 = 0", suggesting initialization.
%               % The update (16) has tau. Let's assume tau is an algorithmic parameter.
%               % If the paper doesn't specify tau for the update, we might need to set it.
%               % Often, tau is set to 0 for initialization of lambda, and then for updates, it's a positive step.
%               % Let's use a placeholder and see if the paper gives more clues or use a common value.
%               % The VMD code by Dragomiretskiy (original author) uses tau for the lambda update.
%               % Let's assume tau is for the update step.
% tol_cvmd = 1e-7; % 收敛容限 (Convergence tolerance)
% max_iter_cvmd = 100; % 最大迭代次数 (Maximum iterations)
% N_tm2 = Num_tm2; % 方位向采样点数 (Number of azimuth samples)
% N_r = Num_r;     % 距离向采样点数 (Number of range samples)
% 
% s_compensated_r_tm = zeros(N_r, N_tm2, 'like', 1j); % 存储补偿后的信号
% 
% % --- ICVMD Processing Loop for each range cell ---
% fprintf('开始ICVMD处理...\n');
% for r_idx = 1:N_r
%     if mod(r_idx, 10) == 0
%         fprintf('处理距离单元: %d / %d\n', r_idx, N_r);
%     end
% 
%     signal_r = s_r_tm2(r_idx, :); % 当前距离单元的信号 (Signal of the current range cell)
%     S_r_omega = fft(signal_r);    % 信号的频谱 (Spectrum of the signal)
% 
%     % --- ICVMD 初始化 (Initialization) ---
%     u_k_tm = zeros(K, N_tm2, 'like', 1j); % 时域模式 (Time-domain modes)
%     U_k_omega = zeros(K, N_tm2, 'like', 1j); % 频域模式 (Frequency-domain modes)
%     omega_k = zeros(K, 1); % 中心频率 (Center frequencies)
% 
%     % 初始化中心频率 omega_k (e.g., spread over half the spectrum)
%     for k_idx = 1:K
%         omega_k(k_idx) = (k_idx - 1) * (0.5 / K); % Normalized frequency (0 to 0.5)
%     end
% 
%     % 初始化模式 U_k_omega (e.g., by splitting the spectrum)
%     % This is a simple initialization; more sophisticated ones might exist.
%     % For simplicity, let's initialize u_k_tm to small random values or zeros,
%     % and U_k_omega as their FFT. Or initialize U_k_omega directly.
%     % The paper by default initializes u_k to zero.
%     % Let's initialize U_k_omega by dividing the signal S_r_omega among modes.
%     % For example, U_k_omega = S_r_omega / K for all k initially.
%     % Or, more simply, initialize u_k_tm as zeros, so U_k_omega are zeros.
%     % The first update of U_k will then use S_r_omega.
% 
%     lambda_omega = zeros(1, N_tm2, 'like', 1j); % 拉格朗日乘子 (Lagrange multiplier)
% 
%     % 频率轴 (Frequency axis for omega in formulas, normalized 0 to 1 for FFT)
%     omega_axis_norm = (0:N_tm2-1) / N_tm2; 
% 
%     % --- ICVMD 迭代 (Iteration) ---
%     for iter = 1:max_iter_cvmd
%         U_k_omega_prev_iter = U_k_omega; % Store for convergence check
% 
%         % 1. 更新模式 u_k (Update modes u_k via U_k_omega)
%         sum_U_k_except_current = zeros(1, N_tm2, 'like', 1j);
%         for k_idx = 1:K
%             % Calculate sum of U_j for j != k_idx
%             sum_U_j_neq_k = sum(U_k_omega(1:K ~= k_idx, :), 1); % Sum over other modes
% 
%             numerator = S_r_omega - sum_U_j_neq_k + lambda_omega/2;
%             denominator = 1 + 2 * alpha_cvmd * (omega_axis_norm - omega_k(k_idx)).^2;
%             U_k_omega(k_idx, :) = numerator ./ denominator;
%         end
% 
%         % 2. 更新中心频率 omega_k (Update center frequencies omega_k)
%         for k_idx = 1:K
%             % Integral of omega * |U_k|^2 / Integral of |U_k|^2
%             % omega_axis_norm is normalized frequency from 0 to almost 1.
%             % The paper's omega_k is also likely normalized or in rad/sample.
%             % Ensure consistency. If omega_axis_norm is [0, 1), then omega_k should be in this range.
%             integrand_num = omega_axis_norm .* abs(U_k_omega(k_idx, :)).^2;
%             integrand_den = abs(U_k_omega(k_idx, :)).^2;
%             if sum(integrand_den) == 0 % Avoid division by zero
%                 omega_k(k_idx) = omega_axis_norm(floor(N_tm2/2) + 1); % Default to center if mode is zero
%             else
%                 omega_k(k_idx) = sum(integrand_num) / sum(integrand_den);
%             end
%         end
% 
%         % 3. 更新拉格朗日乘子 lambda (Update Lagrange multiplier lambda)
%         % The paper uses tau in eq (16). If tau=0 is only for init, what is it for update?
%         % Let's assume a default tau_update, e.g., 0.1, or check VMD literature.
%         % Original VMD paper (Dragomiretskiy 2014) uses tau for lambda update.
%         % If tau_cvmd from params is 0, this update does nothing.
%         % Let's set a specific tau for the update, or assume it's implicitly 1 if not scaled.
%         % The paper's algorithm description (16) shows lambda_i^{n+1}(omega) = lambda_i^n(omega) + tau (S_i(omega) - sum_k U_{k,i}^{n+1}(omega))
%         % The parameter 'tau_cvmd' might be this 'tau'. If it was set to 0 for initialization, it needs a value for update.
%         % Let's assume tau_cvmd is the update tau. If it's 0 from params, it means no update or a misunderstanding.
%         % Given the user's code has `tau_cvmd = 0;`, this is problematic for lambda update.
%         % Let's assume a default `tau_update_lambda = 0.1` or some other reasonable value if `tau_cvmd` is zero.
%         % Or, the paper might imply a specific way.
%         % Re-reading: "Initialize... lambda_i^1 = 0". This is initialization.
%         % The update equation (16) must use a non-zero tau.
%         % The paper does not explicitly state the value of tau used in simulations for the update of lambda.
%         % Typical VMD implementations use a tau that can affect convergence speed.
%         % Let's use a moderate default if `tau_cvmd` is zero, and add a note.
%         tau_update_lambda_val = tau_cvmd; 
%         if tau_update_lambda_val == 0
%             % This is a guess if tau is not properly set for update based on paper.
%             % A common strategy is to not have tau if quadratic penalty is large enough.
%             % Or tau is related to alpha.
%             % For now, let's try a simple residual addition (equivalent to tau=1).
%             tau_update_lambda_val = 1; % Or some other default like 0.1, 0.01
%             % fprintf('Warning: tau_cvmd was 0, using %f for lambda update.\n', tau_update_lambda_val);
%         end
%         sum_U_k_current_iter = sum(U_k_omega, 1);
%         lambda_omega = lambda_omega + tau_update_lambda_val * (S_r_omega - sum_U_k_current_iter);
% 
%         % 4. 检查收敛性 (Check convergence)
%         diff_U = sum(sum(abs(U_k_omega - U_k_omega_prev_iter).^2)) / sum(sum(abs(U_k_omega_prev_iter).^2));
%         if diff_U < tol_cvmd && iter > 1 % iter > 1 to ensure U_k_omega_prev_iter is not initial zeros
%             %fprintf('Range cell %d converged in %d iterations.\n', r_idx, iter);
%             break;
%         end
%     end % End of CVMD iterations
% 
%     if iter == max_iter_cvmd
%         %fprintf('Range cell %d reached max iterations.\n', r_idx);
%     end
% 
%     % --- 将频域模式转换回时域 (Convert frequency-domain modes back to time domain) ---
%     for k_idx = 1:K
%         u_k_tm(k_idx, :) = ifft(U_k_omega(k_idx, :));
%     end
% 
%     % --- 相位误差估计 (Phase Error Estimation) ---
%     % 选择主导模式 (Select dominant mode) - e.g., mode with highest energy
%     mode_energies = sum(abs(u_k_tm).^2, 2);
%     [~, dominant_mode_idx] = max(mode_energies);
%     u_dominant_tm = u_k_tm(dominant_mode_idx, :);
% 
%     % 估计瞬时频率 (Estimate Instantaneous Frequency - IF)
%     % Using hilbert to get analytic signal, then unwrap phase and differentiate
%     analytic_signal_dominant = hilbert(u_dominant_tm);
%     instantaneous_phase = unwrap(angle(analytic_signal_dominant));
%     % IF (normalized frequency: cycles/sample)
%     % Add a small epsilon to avoid issues with diff on potentially constant phase start/end
%     instantaneous_freq_norm = diff(instantaneous_phase) / (2*pi); 
%     % Pad to original length (e.g., repeat last value)
%     instantaneous_freq_norm = [instantaneous_freq_norm, instantaneous_freq_norm(end)]; 
% 
%     % 论文中提到: phi_e,i(t) = 2*pi * integral_0^t (IF_k0,i(tau) - mean(IF_k0,i)) dtau
%     % IF_k0,i is the IF of the dominant mode.
%     % mean_IF = mean(instantaneous_freq_norm); % Mean IF
%     % demeaned_IF = instantaneous_freq_norm - mean_IF;
%     % phase_error_rad_per_sample = 2 * pi * cumsum(demeaned_IF); % Integration
% 
%     % Alternative from some autofocus: use the phase of the dominant scatterer directly
%     % The paper's equation (17) is phi_e,i(t) = angle(u_k0,i(t)) - 2*pi*f_c_k0,i * t
%     % where f_c_k0,i is the center frequency of the k0-th mode.
%     % This is simpler. omega_k(dominant_mode_idx) is the normalized center frequency.
%     % Need to convert it to cycles/sample if tm2 is sample index.
%     % omega_k is already normalized [0,1). So f_c_k0_norm = omega_k(dominant_mode_idx).
%     f_c_dominant_norm = omega_k(dominant_mode_idx);
%     phase_error_est = angle(u_dominant_tm) - 2 * pi * f_c_dominant_norm .* (0:N_tm2-1);
%     % This phase error might need unwrapping or careful handling.
%     % Let's stick to the IF integration method first as it's more common for phase error.
% 
%     % Re-evaluating phase error estimation from paper:
%     % Eq (17) in paper: phi_e,i(t_m) = angle(u_k0,i(t_m)) - omega_k0 * t_m
%     % Here omega_k0 is the center *angular* frequency.
%     % Our omega_k is normalized frequency f_norm (cycles/sample). So omega_k0_angular = 2*pi*f_norm.
%     % t_m is the slow time. If using sample indices, t_m = 0, 1, ..., N_tm2-1.
%     % So, phase_error_est = angle(u_dominant_tm) - 2 * pi * omega_k(dominant_mode_idx) .* (0:N_tm2-1);
%     % This estimates the deviation from a pure sinusoid at the mode's center frequency.
% 
%     % Let's use the IF integration method as it's more robust for general phase errors.
%     mean_IF_dominant = mean(instantaneous_freq_norm);
%     phase_error_rad = 2 * pi * cumsum(instantaneous_freq_norm - mean_IF_dominant); % This is the phase error phi_e,i(t)
% 
%     % --- 相位补偿 (Phase Compensation) ---
%     s_compensated_r_tm(r_idx, :) = signal_r .* exp(-1j * phase_error_rad);
% 
% end % End of loop over range cells
% fprintf('ICVMD处理完成。\n');
% 
% % --- 方位向FFT成像 (Azimuth FFT for Imaging) ---
% % ISAR_image_cvmd = fftshift(fft(s_compensated_r_tm, [], 2), 2);
% % For ISAR, often we do FFT, then fftshift along azimuth.
% % And sometimes, no fftshift on range if already centered.
% ISAR_image_cvmd = fftshift(fft(s_compensated_r_tm, N_tm2, 2), 2); % FFT along slow time (dim 2)
% 
% % --- 显示图像 (Display Image) ---
% figure;
% imagesc(tm2, r, abs(ISAR_image_cvmd)); % Use tm2 for x-axis (Doppler/cross-range related) and r for y-axis (range)
% % The paper's Fig 5 uses "Azimuth Cell" and "Range Cell"
% % So, x-axis could be Doppler bins, y-axis could be range bins.
% % Let's use indices for now, or scaled axes if preferred.
% % imagesc(abs(ISAR_image_cvmd));
% xlabel('方位单元 (Azimuth Cell / Doppler)');
% ylabel('距离单元 (Range Cell)');
% title('ICVMD ISAR 成像结果 (ICVMD ISAR Imaging Result)');
% colormap('jet'); % Or 'jet' or other colormaps used in ISAR
% axis xy; % Ensure range direction is typical
% colorbar;
% 
% % To better match Fig 5, we might need to:
% % 1. Normalize the image.
% % 2. Adjust dynamic range (e.g., display in dB).
% %    ISAR_image_cvmd_db = 20*log10(abs(ISAR_image_cvmd) / max(abs(ISAR_image_cvmd(:))));
% %    imagesc(ISAR_image_cvmd_db);
% %    caxis([-40, 0]); % Display dynamic range, e.g., -40dB to 0dB
% 
% % Let's try dB scale for display
% max_val = max(abs(ISAR_image_cvmd(:)));
% if max_val == 0, max_val = 1; end % Avoid log10(0)
% ISAR_image_cvmd_db = 20*log10(abs(ISAR_image_cvmd) / max_val);
% 
% figure;
% imagesc(ISAR_image_cvmd_db);
% xlabel('方位单元 (Azimuth Cell / Doppler)');
% ylabel('距离单元 (Range Cell)');
% title('ICVMD ISAR 成像结果 (dB) (ICVMD ISAR Imaging Result in dB)');
% colormap('jet');
% axis xy;
% colorbar;
% % Adjust caxis for better visualization, e.g., typical -30dB or -40dB dynamic range
% caxis_min = -30; % dB
% caxis_max = 0;   % dB
% caxis([caxis_min, caxis_max]);
% 
% fprintf('脚本执行完毕。\n');

%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%

% clc; clear all; close all;
% %-----------------------------散射点模型 ------------------------------%
% Pos = [-10 -1 0;-9 -1 0;-8 -1 0;-7 -1 0;-6 -1 0;-5 -1 0;-3.75 -1 0;-3 -1 0;-2 -1 0;-1 -1 0;...   %(10个) 
%        0 -1 0;...      %(1个)
%        1 -1 0;2 -1 0;3 -1 0;4 -1 0;5 -1 0;6 -1 0;7 -1 0;8 -1 0;9 -1 0;10 -1 0;...  %(10个)
%        -9.5 0.2 0.5;...    %(1个)
%        -9.5 1.2 0.5;-9 1 0;-8 1 0;-7 1 0;-6 1 0;-5.2 1.2 0;-4.1 1 0;-3 1 0;-2 1 0;-1 1 0;...  %(10个)
%        0 1 0;...      %(1个)
%        1 1 0;2 1 0;3 1 0;4 1 0;5 1 0;6 1 0;7 1 0;8 1 0;9 1 0;10 1 0;...     %(10个)
%        10.5 -0.75 0;10.5 0.75 0;11 -0.5 0;11 0.5 0;11.5 0 0;... %尾部        %(5个)
%        9.5 0.5 0.5;9.5 -0.5 0.5;9 0 0.5;8.5 0.5 0.5;8.5 -0.5 0.5;... %头部 % (5个) 9.5 0 0.5;9 0.5 0.5;9 -0.5 0.5;8.5 0 0.5;
%        5 0 0.5;5 0 1;5 0 1.5;5 0 2;5 0 2.5;5 0 3;5 0 3.5;5 0 4;...   %机头 % (8个)
%        5.5 0.5 0.5;5.5 -0.5 0.5;4.5 0.5 0.5;4.5 -0.5 0.5;... %机头运动 % (4个)5 0 0.5; 5.5 0 0.5;5 0.5 0.5;5 -0.5 0.5; 4.5 0 0.5;  
%        0.5 0.5 0.9;0.5 -0.5 0.9;-0.5 0.5 0.9;-0.5 -0.5 0.9;0 0 0.5;... %中间运动 % (5个) 0.5 0 1;-0.5 0 1;0 0.5 1;0 -0.5 1;
%        -5 0 0.2;-5 0.2 0.8;-5 0.2 1.4;-5 0 2;... %机尾部 % (4个) 
%        -5.5 -0.5 0.5;-5.5 0.5 0.5;-4.4 0.5 0.5;-4.5 -0.6 0.5;...%机尾运动 % (4个)-5 0 0.5;-5.5 0 0.5; -5 0.5 0.5;-5 -0.5 0.5;-4.5 0 0.5;
%        ];
% 
% %-------------------------------------------------------------------------%
% %-------------------------------    显示   -------------------------------%
% x_Pos = Pos(:,1)*5;
% y_Pos = Pos(:,2)*5;
% z_Pos = Pos(:,3)*5;
% % 坐标归一化处理，将坐标原点移至目标中心附近 (此处简单处理，实际应基于目标几何中心)
% % 为了与论文中目标位于坐标系中心附近对应，此处平移使得x,y,z均有正有负值，且大致对称
% % min_x_Pos = min(x_Pos);
% % x_Pos = x_Pos - min_x_Pos - (max(x_Pos)-min_x_Pos)/2; %中心化x
% % min_y_Pos = min(y_Pos);
% % y_Pos = y_Pos - min_y_Pos - (max(y_Pos)-min_y_Pos)/2; %中心化y
% % min_z_Pos = min(z_Pos);
% % z_Pos = z_Pos - min_z_Pos - (max(z_Pos)-min_z_Pos)/2; %中心化z
% 
% figure;
% plot3(x_Pos,y_Pos,z_Pos,'*');
% grid on;
% xlabel('X (m)'); ylabel('Y (m)'); zlabel('Z (m)');
% title('目标散射点模型');
% 
% R = [cos(3*pi/8)*cos(0),cos(3*pi/8)*sin(0),sin(3*pi/8)];  %雷达目标视线单位矢量
% Num_point = length(x_Pos); %目标点数
% x_r = zeros(1,Num_point);
% y_r = zeros(1,Num_point);
% z_r = zeros(1,Num_point);
% for n_point = 1:Num_point
%     x_r(n_point) = y_Pos(n_point)*R(3)-z_Pos(n_point)*R(2);
%     y_r(n_point) = z_Pos(n_point)*R(1)-x_Pos(n_point)*R(3);
%     z_r(n_point) = x_Pos(n_point)*R(2)-y_Pos(n_point)*R(1);
% end
% x_oumiga = 0.05; %目标旋转初始角速度
% y_oumiga = 0.2; %
% z_oumiga = 0.05;
% x_lamda = 0.05; %0.05%目标旋转角速度加速度
% y_lamda = 0.1; %0.1
% z_lamda = 0.05; %0.05
% x_gamma = 0.05; %0.05%目标旋转角速度加加速度
% y_gamma = 0.4; %0.4
% z_gamma = 0.05; %0.05
% f_v = zeros(1,Num_point); % 对应论文中的 v_p(t_m) 中的速度项系数 (f(n_point) in your code)
% alpha_a = zeros(1,Num_point); % 对应论文中的 a_p(t_m) 中的加速度项系数 (alpha(n_point) in your code)
% beita_j = zeros(1,Num_point); % 对应论文中的 j_p(t_m) 中的加加速度项系数 (beita(n_point) in your code)
% 
% for n_point = 1:Num_point
%     f_v(n_point) = x_r(n_point)*x_oumiga+y_r(n_point)*y_oumiga+z_r(n_point)*z_oumiga;
%     alpha_a(n_point) = x_r(n_point)*x_lamda+y_r(n_point)*y_lamda+z_r(n_point)*z_lamda;
%     beita_j(n_point) = x_r(n_point)*x_gamma+y_r(n_point)*y_gamma+z_r(n_point)*z_gamma;
% end
% %---------------------------雷达系统参数与时域变量---------------------------%
% B = 80*1e6;  %带宽
% c = 3*1e8; 
% PRF = 1400; %脉冲重复频率
% fc = 5.2*1e9; %载频
% delta_r = c/(2*B); %距离分辨率
% r = -50*delta_r:delta_r:50*delta_r; %距离单元轴
% tm_orig = 0:(1/PRF):0.501; %慢时间轴 (原始，可能用于s_r_tm)
% 
% Num_r = length(r);
% Num_tm_orig = length(tm_orig); % 原始慢时间点数
% ones_r = ones(1,Num_r);
% ones_tm_orig = ones(1,Num_tm_orig);
% 
% % s_r_tm = 0; % 第一个回波模型 (可能对应论文中未补偿的初始图像)
% % for n_point = 1:Num_point
% %     Delta_R0(n_point) = x_Pos(n_point)*R(1)+y_Pos(n_point)*R(2)+z_Pos(n_point)*R(3);%初始时刻的距离值
% %     % 论文中回波模型 R_p(t_m) = R_0p + v_p*t_m + 0.5*a_p*t_m^2 + (1/6)*j_p*t_m^3
% %     % 您的代码中 f_v, alpha_a, beita_j 对应 v_p, a_p, j_p
% %     % 您的代码中 Delta_R = f(n_point).*0+(1/2)*alpha(n_point).*0.*0+(1/6)*beita(n_point).*0.*0.*tm + Delta_R0(n_point);
% %     % 上式似乎只用了 Delta_R0 和一个非常数项的 tm，这可能与论文不完全一致
% %     % 假设 f_v, alpha_a, beita_j 是瞬时值，或者需要乘以时间tm, tm^2, tm^3
% %     % 按照您的代码逻辑 Delta_R 的计算:
% %     % Delta_R_t = Delta_R0(n_point) + f_v(n_point).*tm_orig + (1/2)*alpha_a(n_point).*tm_orig.^2 + (1/6)*beita_j(n_point).*tm_orig.^3; % 修正为完整运动模型
% %     % 您的原始代码中，Delta_R 和 sita_Delta_R 的时间依赖项似乎部分为0，这里我们用 tm_orig
% %     Delta_R_t_orig = Delta_R0(n_point) + f_v(n_point).*tm_orig*0 + (1/2)*alpha_a(n_point).*tm_orig.^2*0 + (1/6)*beita_j(n_point).*tm_orig.^3*0; % 这是您原始代码的项，似乎主要依赖Delta_R0
% 
% %     % sita_Delta_R = 4*pi*(fc/c)*((f_v(n_point)+0*c/(2*fc)).*tm_orig+(1/2)*alpha_a(n_point).*tm_orig.^2*0+(1/6)*beita_j(n_point).*tm_orig.^3*0 + Delta_R0(n_point));
% %     % 修正为完整的相位模型: phase = 4*pi*fc/c * R_p(t_m)
% %     phase_t_orig = (4*pi*fc/c) * (Delta_R0(n_point) + f_v(n_point).*tm_orig + (1/2)*alpha_a(n_point).*tm_orig.^2 + (1/6)*beita_j(n_point).*tm_orig.^3);
% 
% %     s_r_tm = s_r_tm + sinc((2*B/c)*(r.'*ones_tm_orig-ones_r.'*Delta_R_t_orig)).*exp(1j*ones_r.'*phase_t_orig); % 使用修正后的完整模型
% % end
% 
% % figure
% % imagesc(flipud(abs(fft(s_r_tm,Num_tm_orig,2))))  %原始代码中的显示
% 
% tm2 = 0:(1/PRF):0.501; % CVMD处理用的慢时间轴
% Num_tm2 = length(tm2); % CVMD处理用的慢时间点数
% ones_tm2 = ones(1,Num_tm2);
% s_r_tm2 = 0;  % CVMD的输入回波 (Range compressed data)
% Delta_R0 = zeros(1,Num_point); % 初始化
% 
% fprintf('正在生成ISAR回波数据 s_r_tm2...\n');
% for n_point = 1:Num_point
%     Delta_R0(n_point) = x_Pos(n_point)*R(1)+y_Pos(n_point)*R(2)+z_Pos(n_point)*R(3);%初始时刻的距离值
% 
%     % 目标到雷达的瞬时距离 R_p(t_m) = R_0p + v_p*t_m + 0.5*a_p*t_m^2 + (1/6)*j_p*t_m^3
%     % R_0p 是初始距离 Delta_R0(n_point)
%     % v_p, a_p, j_p 分别是 f_v(n_point), alpha_a(n_point), beita_j(n_point)
%     Delta_R_t = Delta_R0(n_point) + f_v(n_point).*tm2 + (1/2)*alpha_a(n_point).*tm2.^2 + (1/6)*beita_j(n_point).*tm2.^3;
% 
%     % 相位项 phi_p(t_m) = 4*pi*f_c*R_p(t_m)/c
%     sita_Delta_R_t = (4*pi*fc/c) * Delta_R_t;
% 
%     % 距离压缩后的信号 s(r, t_m) = sum_p sigma_p * rect((r-R_p(t_m))/delta_r) * exp(-j*phi_p(t_m))
%     % rect(...) 变为 sinc(...) in range profile after pulse compression
%     % 您的代码中已经包含了幅度的变化
%     amplitude_factor = 1;
%     if n_point>53 && n_point<62
%         amplitude_factor = 1.3; % 论文或模型中特定散射点的增强
%     end
%     if n_point == 48 % 论文或模型中特定散射点的增强
%         amplitude_factor = amplitude_factor * 1; % 原始代码中这里是乘以1，可以按需调整
%     end
% 
%     s_r_tm2 = s_r_tm2 + amplitude_factor * sinc((2*B/c)*(r.'*ones_tm2 - ones_r.'*Delta_R_t)) .* exp(1j*ones_r.'*sita_Delta_R_t);
% end
% fprintf('ISAR回波数据 s_r_tm2 生成完毕。\n');
% 
% % %---------------------------- 论文中提到的初始多普勒补偿--------------------------------%
% % s_phase_comp1 = exp(1j*ones_r.'*(2*pi*((1/2)*40.*tm2.*tm2+(1/6)*400.*tm2.*tm2.*tm2)));  %二次和三次相位补偿项
% % s_phase_comp =exp(1j*ones_r.'*(2*pi*(190.*tm2+(1/2)*40.*tm2.*tm2+(1/6)*400.*tm2.*tm2.*tm2)));  %完整相位补偿
% s_phase_comp =exp(1j*ones_r.'*(2*pi*(190.*tm2))); %您的代码中仅包含线性项 (多普勒频移补偿)
% s_r_tm2 = s_r_tm2 .* s_phase_comp; % 应用初始补偿
% 
% % --- ICVMD 算法参数 ---
% K = 3; % 模式数量 (Number of modes) - 根据论文Fig5实验设置或目标特性调整
% alpha_cvmd = 2000; % 平衡参数 (Balancing parameter) - 论文中常用值
% tau_cvmd_param = 0; % 您代码中的原始 tau 值
% tol_cvmd = 1e-7; % 收敛容限 (Convergence tolerance)
% max_iter_cvmd = 100; % 最大迭代次数 (Maximum iterations)
% N_tm = Num_tm2; % 方位向采样点数 (Number of azimuth samples)
% N_r = Num_r;     % 距离向采样点数 (Number of range samples)
% 
% s_compensated_r_tm = zeros(N_r, N_tm, 'like', 1j); % 存储补偿后的信号
% 
% % --- ICVMD 处理回波 s_r_tm2 ---
% fprintf('开始ICVMD处理 (共 %d 个距离单元)...\n', N_r);
% 
% % 频率轴 (归一化频率，范围 [0, 1) )
% omega_axis_norm = (0:N_tm-1) / N_tm;
% 
% % tau_cvmd_param 来自您的代码，如果为0，lambda的更新将无效。
% % 论文(16)中 lambda 更新项包含 tau。若无明确指定，则使用一个默认值。
% tau_update_lambda = tau_cvmd_param;
% if tau_update_lambda == 0
%     tau_update_lambda = 0.1; % 使用一个较小的默认值或根据经验调整，例如0.1, 1
%     fprintf('警告: 输入参数 tau_cvmd 为0, lambda更新时将使用默认值 tau = %f\n', tau_update_lambda);
% end
% 
% 
% for r_idx = 1:N_r
%     if mod(r_idx, round(N_r/10)) == 0 || r_idx == 1 || r_idx == N_r % 每处理10%的距离单元或首尾时打印进度
%         fprintf('处理距离单元: %d / %d\n', r_idx, N_r);
%     end
% 
%     signal_r_tm = s_r_tm2(r_idx, :); % 当前距离单元的信号 (时域)
%     S_r_omega = fft(signal_r_tm);    % 信号的频谱 (频域)
% 
%     % --- ICVMD 初始化 (针对当前距离单元) ---
%     U_k_omega_current_iter = zeros(K, N_tm, 'like', 1j); % 当前迭代的频域模式 U_k^{n+1}
%     omega_k_current_iter = zeros(K, 1);           % 当前迭代的中心频率 omega_k^{n+1}
% 
%     % 初始化中心频率 omega_k (均匀分布在 [0, 0.5*PRF) 对应的归一化频率)
%     for k_init_idx = 1:K
%         omega_k_current_iter(k_init_idx) = (k_init_idx - 1) * (0.5 / K); % 归一化频率 [0, 0.5)
%     end
% 
%     % 初始化拉格朗日乘子 lambda (频域)
%     lambda_omega_current_iter = zeros(1, N_tm, 'like', 1j); % lambda^n
% 
%     % --- ICVMD 迭代 ---
%     for iter = 1:max_iter_cvmd
%         % 保存上一次迭代的结果，用于计算 U_k^{n+1} 和检查收敛
%         U_k_omega_prev_iter = U_k_omega_current_iter; % U_k^n
%         omega_k_prev_iter = omega_k_current_iter;     % omega_k^n
%         lambda_omega_prev_iter = lambda_omega_current_iter; % lambda^n
% 
%         % 1. 更新模式 U_k (计算 U_k^{n+1})
%         % U_k^{n+1}(omega) = (S(omega) - sum_{j!=k} U_j^n(omega) + lambda^n(omega)/2) / (1 + 2*alpha*(omega - omega_k^n)^2)
%         temp_U_k_next_iter = zeros(K, N_tm, 'like', 1j);
%         for k_update_idx = 1:K
%             sum_U_j_neq_k_val = sum(U_k_omega_prev_iter(1:K ~= k_update_idx, :), 1);
% 
%             numerator_U = S_r_omega - sum_U_j_neq_k_val + lambda_omega_prev_iter/2;
%             denominator_U = 1 + 2 * alpha_cvmd * (omega_axis_norm - omega_k_prev_iter(k_update_idx)).^2;
% 
%             temp_U_k_next_iter(k_update_idx, :) = numerator_U ./ denominator_U;
%         end
%         U_k_omega_current_iter = temp_U_k_next_iter; % U_k^{n+1}
% 
%         % 2. 更新中心频率 omega_k (计算 omega_k^{n+1})
%         % omega_k^{n+1} = integral(omega * |U_k^{n+1}(omega)|^2 d_omega) / integral(|U_k^{n+1}(omega)|^2 d_omega)
%         temp_omega_k_next_iter = zeros(K,1);
%         for k_update_idx = 1:K
%             % 使用当前迭代计算出的 U_k^{n+1}
%             abs_U_k_sq = abs(U_k_omega_current_iter(k_update_idx, :)).^2;
%             integrand_num_omega = omega_axis_norm .* abs_U_k_sq;
% 
%             sum_abs_U_k_sq = sum(abs_U_k_sq);
%             if sum_abs_U_k_sq < 1e-12 % 避免除以零或极小值导致的不稳定
%                 temp_omega_k_next_iter(k_update_idx) = omega_k_prev_iter(k_update_idx); % 若模式能量过小，保持前值
%             else
%                 temp_omega_k_next_iter(k_update_idx) = sum(integrand_num_omega) / sum_abs_U_k_sq;
%             end
%         end
%         omega_k_current_iter = temp_omega_k_next_iter; % omega_k^{n+1}
% 
%         % 3. 更新拉格朗日乘子 lambda (计算 lambda^{n+1})
%         % lambda^{n+1}(omega) = lambda^n(omega) + tau * (S(omega) - sum_k U_k^{n+1}(omega))
%         sum_U_k_all_current_iter = sum(U_k_omega_current_iter, 1); % Sum of U_k^{n+1}
%         lambda_omega_current_iter = lambda_omega_prev_iter + tau_update_lambda * (S_r_omega - sum_U_k_all_current_iter); % lambda^{n+1}
% 
%         % 4. 检查收敛性
%         % 收敛条件: sum_k ||U_k^{n+1} - U_k^n||_2^2 / ||U_k^n||_2^2 < tol
%         diff_U_sq_sum = 0;
%         norm_U_prev_sq_sum = 0;
%         for k_conv_idx = 1:K
%             diff_U_sq_sum = diff_U_sq_sum + sum(abs(U_k_omega_current_iter(k_conv_idx,:) - U_k_omega_prev_iter(k_conv_idx,:)).^2);
%             norm_U_prev_sq_sum = norm_U_prev_sq_sum + sum(abs(U_k_omega_prev_iter(k_conv_idx,:)).^2);
%         end
% 
%         if norm_U_prev_sq_sum < 1e-12 % 如果前一迭代的模态能量总和过小，避免除零
%             if diff_U_sq_sum < tol_cvmd % 如果差异也很小，则认为已收敛
%                  break;
%             end
%         elseif (diff_U_sq_sum / norm_U_prev_sq_sum) < tol_cvmd && iter > 1 
%             break; % 收敛
%         end
%     end % End of CVMD iterations for one range cell
% 
%     % --- 将频域模式转换回时域 u_k(t_m) ---
%     u_k_tm_decomposed = zeros(K, N_tm, 'like', 1j);
%     for k_idx_ifft = 1:K
%         u_k_tm_decomposed(k_idx_ifft, :) = ifft(U_k_omega_current_iter(k_idx_ifft, :));
%     end
% 
%     % --- 相位误差估计 (根据论文 Eq. 17) ---
%     % 选择主导模式 (能量最大的模式)
%     mode_energies = sum(abs(u_k_tm_decomposed).^2, 2);
%     [~, dominant_mode_idx] = max(mode_energies);
%     u_dominant_tm = u_k_tm_decomposed(dominant_mode_idx, :);
% 
%     % 获取主导模式的中心频率 (归一化)
%     f_center_dominant_norm = omega_k_current_iter(dominant_mode_idx); % omega_k已经是归一化频率 (cycles/sample)
% 
%     % 慢时间采样点索引
%     tm_indices = 0:N_tm-1;
% 
%     % 计算相位误差 phi_e(t_m) = angle(u_k0(t_m)) - omega_k0 * t_m
%     % omega_k0 * t_m 对应 2*pi*f_center_dominant_norm * tm_indices
%     phase_error_est_rad = angle(u_dominant_tm) - 2 * pi * f_center_dominant_norm .* tm_indices;
% 
%     % 对估计的相位误差进行解缠绕和平滑处理 (可选，但通常有益)
%     phase_error_est_rad = unwrap(phase_error_est_rad);
%     % 移除均值，消除常数相位偏移 (不影响聚焦，但使误差项更标准)
%     phase_error_est_rad = phase_error_est_rad - mean(phase_error_est_rad);
% 
%     % --- 相位补偿 ---
%     s_compensated_r_tm(r_idx, :) = signal_r_tm .* exp(-1j * phase_error_est_rad);
% 
% end % End of loop over range cells
% fprintf('ICVMD处理完成。\n');
% 
% % --- 方位向FFT成像 ---
% % ISAR图像通过对补偿后的信号沿慢时间做FFT得到
% % N_tm 是方位向点数，[]表示自动选择长度，2表示沿第二维(慢时间维)操作
% ISAR_image_cvmd = fftshift(fft(s_compensated_r_tm, N_tm, 2), 2);
% 
% % --- 显示ICVMD处理后的ISAR图像 ---
% figure;
% max_val = max(abs(ISAR_image_cvmd(:)));
% if max_val == 0, max_val = 1; end % 避免log10(0)
% ISAR_image_cvmd_db = 20*log10(abs(ISAR_image_cvmd) / max_val);
% 
% imagesc(ISAR_image_cvmd_db); % 直接用单元索引作为坐标轴
% xlabel('方位单元 (Azimuth Cell / Doppler)');
% ylabel('距离单元 (Range Cell)');
% title('ICVMD ISAR 成像结果 (dB)');
% colormap('jet'); % 灰度图，与论文中类似
% axis xy; % 确保距离轴方向正确 (通常y轴原点在下方)
% colorbar;
% % 设置动态范围，例如 -40dB 到 0dB，以更好地观察细节
% caxis_min_db = -30; 
% caxis_max_db = 0;   
% caxis([caxis_min_db, caxis_max_db]);
% 
% fprintf('脚本执行完毕。\n');


%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%-----------------------------散射点模型 ------------------------------%
Pos = [-10 -1 0;-9 -1 0;-8 -1 0;-7 -1 0;-6 -1 0;-5 -1 0;-3.75 -1 0;-3 -1 0;-2 -1 0;-1 -1 0;...   %(10个) 
       0 -1 0;...      %(1个)
       1 -1 0;2 -1 0;3 -1 0;4 -1 0;5 -1 0;6 -1 0;7 -1 0;8 -1 0;9 -1 0;10 -1 0;...  %(10个)
       -9.5 0.2 0.5;...    %(1个)
       -9.5 1.2 0.5;-9 1 0;-8 1 0;-7 1 0;-6 1 0;-5.2 1.2 0;-4.1 1 0;-3 1 0;-2 1 0;-1 1 0;...  %(10个)
       0 1 0;...      %(1个)
       1 1 0;2 1 0;3 1 0;4 1 0;5 1 0;6 1 0;7 1 0;8 1 0;9 1 0;10 1 0;...     %(10个)
       10.5 -0.75 0;10.5 0.75 0;11 -0.5 0;11 0.5 0;11.5 0 0;... %尾部        %(5个)
       9.5 0.5 0.5;9.5 -0.5 0.5;9 0 0.5;8.5 0.5 0.5;8.5 -0.5 0.5;... %头部 % (5个) 9.5 0 0.5;9 0.5 0.5;9 -0.5 0.5;8.5 0 0.5;
       5 0 0.5;5 0 1;5 0 1.5;5 0 2;5 0 2.5;5 0 3;5 0 3.5;5 0 4;...   %机头 % (8个)
       5.5 0.5 0.5;5.5 -0.5 0.5;4.5 0.5 0.5;4.5 -0.5 0.5;... %机头运动 % (4个)5 0 0.5; 5.5 0 0.5;5 0.5 0.5;5 -0.5 0.5; 4.5 0 0.5;  
       0.5 0.5 0.9;0.5 -0.5 0.9;-0.5 0.5 0.9;-0.5 -0.5 0.9;0 0 0.5;... %中间运动 % (5个) 0.5 0 1;-0.5 0 1;0 0.5 1;0 -0.5 1;
       -5 0 0.2;-5 0.2 0.8;-5 0.2 1.4;-5 0 2;... %机尾部 % (4个) 
       -5.5 -0.5 0.5;-5.5 0.5 0.5;-4.4 0.5 0.5;-4.5 -0.6 0.5;...%机尾运动 % (4个)-5 0 0.5;-5.5 0 0.5; -5 0.5 0.5;-5 -0.5 0.5;-4.5 0 0.5;
       ];

%-------------------------------------------------------------------------%
%-------------------------------    显示   -------------------------------%
x_Pos = Pos(:,1)*5;
y_Pos = Pos(:,2)*5;
z_Pos = Pos(:,3)*5;

figure;
plot3(x_Pos,y_Pos,z_Pos,'*');
grid on;
xlabel('X (m)'); ylabel('Y (m)'); zlabel('Z (m)');
title('目标散射点模型');

R = [cos(3*pi/8)*cos(0),cos(3*pi/8)*sin(0),sin(3*pi/8)];  %雷达目标视线单位矢量
Num_point = length(x_Pos); %目标点数
x_r = zeros(1,Num_point);
y_r = zeros(1,Num_point);
z_r = zeros(1,Num_point);
for n_point = 1:Num_point
    x_r(n_point) = y_Pos(n_point)*R(3)-z_Pos(n_point)*R(2);
    y_r(n_point) = z_Pos(n_point)*R(1)-x_Pos(n_point)*R(3);
    z_r(n_point) = x_Pos(n_point)*R(2)-y_Pos(n_point)*R(1);
end
x_oumiga = 0.05; %目标旋转初始角速度
y_oumiga = 0.2; %
z_oumiga = 0.05;
x_lamda = 0.05; %0.05%目标旋转角速度加速度
y_lamda = 0.1; %0.1
z_lamda = 0.05; %0.05
x_gamma = 0.05; %0.05%目标旋转角速度加加速度
y_gamma = 0.4; %0.4
z_gamma = 0.05; %0.05
f_v = zeros(1,Num_point); 
alpha_a = zeros(1,Num_point); 
beita_j = zeros(1,Num_point); 

for n_point = 1:Num_point
    f_v(n_point) = x_r(n_point)*x_oumiga+y_r(n_point)*y_oumiga+z_r(n_point)*z_oumiga;
    alpha_a(n_point) = x_r(n_point)*x_lamda+y_r(n_point)*y_lamda+z_r(n_point)*z_lamda;
    beita_j(n_point) = x_r(n_point)*x_gamma+y_r(n_point)*y_gamma+z_r(n_point)*z_gamma;
end
%---------------------------雷达系统参数与时域变量---------------------------%
B = 80*1e6;  %带宽
c = 3*1e8; 
PRF = 1400; %脉冲重复频率
fc = 5.2*1e9; %载频
delta_r = c/(2*B); %距离分辨率
r = -50*delta_r:delta_r:50*delta_r; %距离单元轴
% tm_orig = 0:(1/PRF):0.501; %慢时间轴 (原始，可能用于s_r_tm)

Num_r = length(r);
% Num_tm_orig = length(tm_orig); % 原始慢时间点数
ones_r = ones(1,Num_r);
% ones_tm_orig = ones(1,Num_tm_orig);


tm2 = 0:(1/PRF):0.501; % CVMD处理用的慢时间轴
Num_tm2 = length(tm2); % CVMD处理用的慢时间点数
ones_tm2 = ones(1,Num_tm2);
s_r_tm2 = 0;  % CVMD的输入回波 (Range compressed data)
Delta_R0 = zeros(1,Num_point); % 初始化

fprintf('正在生成ISAR回波数据 s_r_tm2...\n');
for n_point = 1:Num_point
    Delta_R0(n_point) = x_Pos(n_point)*R(1)+y_Pos(n_point)*R(2)+z_Pos(n_point)*R(3);%初始时刻的距离值

    Delta_R_t = Delta_R0(n_point) + f_v(n_point).*tm2 + (1/2)*alpha_a(n_point).*tm2.^2 + (1/6)*beita_j(n_point).*tm2.^3;
    sita_Delta_R_t = (4*pi*fc/c) * Delta_R_t;

    amplitude_factor = 1;
    if n_point>53 && n_point<62
        amplitude_factor = 1.3; 
    end
    if n_point == 48 
        amplitude_factor = amplitude_factor * 1; 
    end

    s_r_tm2 = s_r_tm2 + amplitude_factor * sinc((2*B/c)*(r.'*ones_tm2 - ones_r.'*Delta_R_t)) .* exp(1j*ones_r.'*sita_Delta_R_t);
end
fprintf('ISAR回波数据 s_r_tm2 生成完毕。\n');
load shipx2.mat;
s_r_tm2=shipx2;
% 初始多普勒补偿 - 根据论文调整为200Hz


% --- ICVMD 算法参数 ---
K = 3; 
alpha_cvmd = 2000; 
tau_cvmd_param = 0; 
tol_cvmd = 1e-7; 
max_iter_cvmd = 500; % 可适当增加此值，如200或500，以确保收敛
[N_r,N_tm]=size(s_r_tm2);
% N_tm = Num_tm2; 
% N_r = Num_r;     

s_compensated_r_tm = zeros(N_r, N_tm, 'like', 1j); 

fprintf('开始ICVMD处理 (共 %d 个距离单元)...\n', N_r);

omega_axis_norm = (0:N_tm-1) / N_tm;

tau_update_lambda = tau_cvmd_param;
if tau_update_lambda == 0
    tau_update_lambda = 0.1; 
    fprintf('警告: 输入参数 tau_cvmd_param 为0, lambda更新时将使用默认值 tau = %f\n', tau_update_lambda);
end


for r_idx = 1:N_r
    if mod(r_idx, round(N_r/10)) == 0 || r_idx == 1 || r_idx == N_r 
        fprintf('处理距离单元: %d / %d\n', r_idx, N_r);
    end

    signal_r_tm = s_r_tm2(r_idx, :); 
    S_r_omega = fft(signal_r_tm);    

    U_k_omega_current_iter = zeros(K, N_tm, 'like', 1j); 
    omega_k_current_iter = zeros(K, 1);           

    % 修改：改进 omega_k 初始化，使其在 [0, 0.5) 内更均匀分布
    for k_init_idx = 1:K
        omega_k_current_iter(k_init_idx) = (k_init_idx - 0.5) * (0.5 / K); 
    end

    lambda_omega_current_iter = zeros(1, N_tm, 'like', 1j); 

    for iter = 1:max_iter_cvmd
        U_k_omega_prev_iter = U_k_omega_current_iter; 
        omega_k_prev_iter = omega_k_current_iter;     
        lambda_omega_prev_iter = lambda_omega_current_iter; 

        temp_U_k_next_iter = zeros(K, N_tm, 'like', 1j);
        for k_update_idx = 1:K
            % 确保使用正确的逻辑索引
            logical_idx_sum = true(1,K);
            logical_idx_sum(k_update_idx) = false;
            if K > 1
                sum_U_j_neq_k_val = sum(U_k_omega_prev_iter(logical_idx_sum, :), 1);
            else
                sum_U_j_neq_k_val = zeros(1, N_tm, 'like', 1j); % 如果 K=1, 则没有其他模式
            end

            numerator_U = S_r_omega - sum_U_j_neq_k_val + lambda_omega_prev_iter/2;
            denominator_U = 1 + 2 * alpha_cvmd * (omega_axis_norm - omega_k_prev_iter(k_update_idx)).^2;

            temp_U_k_next_iter(k_update_idx, :) = numerator_U ./ denominator_U;
        end
        U_k_omega_current_iter = temp_U_k_next_iter; 

        temp_omega_k_next_iter = zeros(K,1);
        for k_update_idx = 1:K
            abs_U_k_sq = abs(U_k_omega_current_iter(k_update_idx, :)).^2;
            integrand_num_omega = omega_axis_norm .* abs_U_k_sq;

            sum_abs_U_k_sq = sum(abs_U_k_sq);
            if sum_abs_U_k_sq < 1e-12 
                temp_omega_k_next_iter(k_update_idx) = omega_k_prev_iter(k_update_idx); 
            else
                temp_omega_k_next_iter(k_update_idx) = sum(integrand_num_omega) / sum_abs_U_k_sq;
            end
        end
        omega_k_current_iter = temp_omega_k_next_iter; 

        sum_U_k_all_current_iter = sum(U_k_omega_current_iter, 1); 
        lambda_omega_current_iter = lambda_omega_prev_iter + tau_update_lambda * (S_r_omega - sum_U_k_all_current_iter); 

        diff_U_sq_sum = 0;
        norm_U_prev_sq_sum = 0;
        for k_conv_idx = 1:K
            diff_U_sq_sum = diff_U_sq_sum + sum(abs(U_k_omega_current_iter(k_conv_idx,:) - U_k_omega_prev_iter(k_conv_idx,:)).^2);
            norm_U_prev_sq_sum = norm_U_prev_sq_sum + sum(abs(U_k_omega_prev_iter(k_conv_idx,:)).^2);
        end

        if norm_U_prev_sq_sum < 1e-12 
            if diff_U_sq_sum < tol_cvmd 
                 break;
            end
        elseif (diff_U_sq_sum / norm_U_prev_sq_sum) < tol_cvmd && iter > 1 
            break; 
        end
    end 

    u_k_tm_decomposed = zeros(K, N_tm, 'like', 1j);
    for k_idx_ifft = 1:K
        u_k_tm_decomposed(k_idx_ifft, :) = ifft(U_k_omega_current_iter(k_idx_ifft, :));
    end

    mode_energies = sum(abs(u_k_tm_decomposed).^2, 2);
    [~, dominant_mode_idx] = max(mode_energies);
    u_dominant_tm = u_k_tm_decomposed(dominant_mode_idx, :);

    f_center_dominant_norm = omega_k_current_iter(dominant_mode_idx); 

    tm_indices = 0:N_tm-1;

    phase_error_est_rad = angle(u_dominant_tm) - 2 * pi * f_center_dominant_norm .* tm_indices;

    phase_error_est_rad = unwrap(phase_error_est_rad);
    phase_error_est_rad = phase_error_est_rad - mean(phase_error_est_rad);

    s_compensated_r_tm(r_idx, :) = signal_r_tm .* exp(-1j * phase_error_est_rad);

end 
fprintf('ICVMD处理完成。\n');

ISAR_image_cvmd = fftshift(fft(s_compensated_r_tm, N_tm, 2), 2);

figure;
max_val = max(abs(ISAR_image_cvmd(:)));
if max_val == 0, max_val = 1; end 
ISAR_image_cvmd_db = 20*log10(abs(ISAR_image_cvmd) / max_val);

imagesc(ISAR_image_cvmd_db); 
xlabel('方位单元 (Azimuth Cell / Doppler)');
ylabel('距离单元 (Range Cell)');
title('ICVMD ISAR 成像结果 (dB) - 初步修正');
colormap('jet'); 
axis xy; 
colorbar;
caxis_min_db = -30; 
caxis_max_db = 0;   
caxis([caxis_min_db, caxis_max_db]);

fprintf('脚本执行完毕。\n');