"""
VMD-ADMM-DCFT融合ISAR成像算法 (Python实现)

本模块实现了变分模态分解(VMD)、交替方向乘数法(ADMM)和离散立方傅里叶变换(DCFT)
三种技术的有机融合，用于舰船ISAR成像。

作者: ISAR算法研究团队
日期: 2024
"""

import numpy as np
import scipy.signal
from scipy.fft import fft, ifft, fftshift, ifftshift
from scipy.optimize import minimize
import matplotlib.pyplot as plt
from typing import Tuple, Dict, Optional, List
import warnings

class VMD_ADMM_DCFT_Fusion:
    """VMD-ADMM-DCFT融合ISAR成像处理器"""
    
    def __init__(self, params: Optional[Dict] = None):
        """
        初始化融合处理器
        
        Args:
            params: 参数字典，包含VMD、ADMM、DCFT和融合参数
        """
        self.params = self._set_default_params(params)
        self.processing_info = {}
        
    def _set_default_params(self, params: Optional[Dict]) -> Dict:
        """设置默认参数"""
        if params is None:
            params = {}
            
        # VMD参数
        vmd_params = params.get('vmd', {})
        vmd_defaults = {
            'K': 4,                    # 模态数量
            'alpha': 2000,             # 平衡参数
            'tau': 0.1,                # 时间步长
            'tol': 1e-7,               # 收敛容限
            'max_iter': 500            # 最大迭代次数
        }
        vmd_params = {**vmd_defaults, **vmd_params}
        
        # DCFT参数
        dcft_params = params.get('dcft', {})
        dcft_defaults = {
            'alpha_range': np.arange(-32, 321, 8),      # 二阶啁啾率范围
            'beta_range': np.arange(-1000, 2401, 200),  # 三阶啁啾率范围
            'gamma_range': np.arange(-500, 1501, 100)   # 四阶啁啾率范围
        }
        dcft_params = {**dcft_defaults, **dcft_params}
        
        # ADMM参数
        admm_params = params.get('admm', {})
        admm_defaults = {
            'rho': 1.0,                # 增广拉格朗日参数
            'lambda': 0.1,             # 稀疏正则化参数
            'max_iter': 100,           # 最大迭代次数
            'tol': 1e-6                # 收敛容限
        }
        admm_params = {**admm_defaults, **admm_params}
        
        # 融合参数
        fusion_params = params.get('fusion', {})
        fusion_defaults = {
            'max_global_iter': 5,      # 全局迭代次数
            'convergence_tol': 1e-4,   # 收敛容限
            'vmd_weight': 0.4,         # VMD权重
            'dcft_weight': 0.3,        # DCFT权重
            'admm_weight': 0.3         # ADMM权重
        }
        fusion_params = {**fusion_defaults, **fusion_params}
        
        # 雷达参数
        radar_params = params.get('radar', {})
        radar_defaults = {
            'fc': 5.2e9,               # 载频 (Hz)
            'B': 80e6,                 # 带宽 (Hz)
            'PRF': 1400                # 脉冲重复频率 (Hz)
        }
        radar_params = {**radar_defaults, **radar_params}
        
        return {
            'vmd': vmd_params,
            'dcft': dcft_params,
            'admm': admm_params,
            'fusion': fusion_params,
            'radar': radar_params
        }
    
    def process(self, echo_data: np.ndarray) -> Tuple[np.ndarray, Dict]:
        """
        执行VMD-ADMM-DCFT融合处理
        
        Args:
            echo_data: 距离压缩后的回波数据 [距离单元 x 方位单元]
            
        Returns:
            ISAR_image: 融合算法成像结果
            processing_info: 处理信息字典
        """
        print("开始VMD-ADMM-DCFT融合处理...")
        
        # 数据预处理
        processed_data = self._preprocess_data(echo_data)
        
        # 初始化融合变量
        fusion_state = self._initialize_fusion_state(processed_data)
        
        # 主迭代循环
        for global_iter in range(self.params['fusion']['max_global_iter']):
            print(f"全局迭代 {global_iter + 1}/{self.params['fusion']['max_global_iter']}")
            
            # VMD-引导的信号分解
            vmd_modes, vmd_freqs = self._vmd_guided_decomposition(
                fusion_state['current_signal'], fusion_state['dcft_params']
            )
            
            # DCFT-增强的相位估计
            phase_estimates, motion_params = self._dcft_enhanced_phase_estimation(
                vmd_modes, vmd_freqs, fusion_state['admm_constraints']
            )
            
            # ADMM-约束的稀疏重建
            sparse_result = self._admm_constrained_reconstruction(
                fusion_state['current_signal'], phase_estimates
            )
            
            # 融合更新和收敛检查
            fusion_state, converged = self._update_fusion_state(
                fusion_state, vmd_modes, phase_estimates, sparse_result
            )
            
            if converged:
                print(f"融合算法在第 {global_iter + 1} 次迭代收敛")
                break
        
        # 生成最终图像
        ISAR_image = self._generate_final_image(fusion_state)
        
        # 生成处理信息
        self.processing_info = self._generate_processing_info(fusion_state)
        
        return ISAR_image, self.processing_info
    
    def _preprocess_data(self, echo_data: np.ndarray) -> np.ndarray:
        """数据预处理"""
        num_range_bins, num_azimuth = echo_data.shape
        
        # 应用汉明窗
        window = scipy.signal.windows.hamming(num_azimuth)
        processed_data = echo_data * window[np.newaxis, :]
        
        # 归一化
        processed_data = processed_data / np.max(np.abs(processed_data))
        
        return processed_data
    
    def _initialize_fusion_state(self, processed_data: np.ndarray) -> Dict:
        """初始化融合状态"""
        return {
            'current_signal': processed_data.copy(),
            'dcft_params': {'alpha': 0, 'beta': 0, 'gamma': 0},
            'admm_constraints': {},
            'convergence_history': [],
            'iteration_count': 0
        }
    
    def _vmd_guided_decomposition(self, signal: np.ndarray, dcft_params: Dict) -> Tuple[np.ndarray, np.ndarray]:
        """VMD-引导的信号分解"""
        num_range_bins, num_azimuth = signal.shape
        K = self.params['vmd']['K']
        
        vmd_modes = np.zeros((K, num_range_bins, num_azimuth), dtype=complex)
        vmd_freqs = np.zeros((K, num_range_bins))
        
        # 对每个距离单元进行VMD分解
        for r_idx in range(num_range_bins):
            range_signal = signal[r_idx, :]
            
            # 执行VMD分解（简化版本）
            modes, freqs = self._perform_vmd_decomposition(range_signal)
            
            vmd_modes[:, r_idx, :] = modes
            vmd_freqs[:, r_idx] = freqs
        
        return vmd_modes, vmd_freqs
    
    def _perform_vmd_decomposition(self, signal: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """执行VMD分解（简化实现）"""
        K = self.params['vmd']['K']
        N = len(signal)
        
        # 初始化
        modes = np.zeros((K, N), dtype=complex)
        freqs = np.zeros(K)
        
        # 简化的VMD实现
        # 在实际应用中，这里应该使用完整的VMD算法
        for k in range(K):
            freqs[k] = (k) / K  # 均匀分布的初始频率
            
            # 生成模态（简化版本）
            t = np.arange(N)
            modes[k, :] = signal * np.exp(1j * 2 * np.pi * freqs[k] * t / N)
        
        return modes, freqs
    
    def _dcft_enhanced_phase_estimation(self, vmd_modes: np.ndarray, vmd_freqs: np.ndarray, 
                                      admm_constraints: Dict) -> Tuple[np.ndarray, Dict]:
        """DCFT-增强的相位估计"""
        K, num_range_bins, num_azimuth = vmd_modes.shape
        phase_estimates = np.zeros((num_range_bins, num_azimuth))
        
        # 生成时间向量
        tm = np.arange(num_azimuth) / self.params['radar']['PRF']
        
        motion_params = {'alpha': 0, 'beta': 0, 'gamma': 0}
        
        # 对每个距离单元进行DCFT相位估计
        for r_idx in range(num_range_bins):
            # 选择主导模态
            mode_energies = np.mean(np.abs(vmd_modes[:, r_idx, :])**2, axis=1)
            dominant_mode = np.argmax(mode_energies)
            
            signal = vmd_modes[dominant_mode, r_idx, :]
            
            # DCFT参数搜索
            best_params = self._dcft_parameter_search(signal, tm, admm_constraints)
            
            # 计算相位估计
            alpha, beta, gamma = best_params['alpha'], best_params['beta'], best_params['gamma']
            phase_poly = alpha * tm**2 + beta * tm**3 + gamma * tm**4
            phase_estimates[r_idx, :] = phase_poly
            
            if r_idx == 0:
                motion_params.update(best_params)
        
        return phase_estimates, motion_params
    
    def _dcft_parameter_search(self, signal: np.ndarray, tm: np.ndarray, 
                             admm_constraints: Dict) -> Dict:
        """DCFT参数搜索"""
        alpha_range = self.params['dcft']['alpha_range']
        beta_range = self.params['dcft']['beta_range']
        gamma_range = self.params['dcft']['gamma_range']
        
        best_params = {'alpha': 0, 'beta': 0, 'gamma': 0}
        best_score = -np.inf
        
        # 简化搜索（在实际应用中可以优化）
        for alpha in alpha_range[::4]:  # 降采样以提高速度
            for beta in beta_range[::4]:
                for gamma in gamma_range[::4]:
                    # 生成补偿信号
                    compensation = np.exp(-1j * 2 * np.pi * (alpha * tm**2 + beta * tm**3 + gamma * tm**4))
                    
                    # 应用补偿
                    compensated = signal * compensation
                    
                    # 计算聚焦度
                    spectrum = fft(compensated)
                    score = np.max(np.abs(spectrum)) / np.mean(np.abs(spectrum))
                    
                    if score > best_score:
                        best_score = score
                        best_params = {'alpha': alpha, 'beta': beta, 'gamma': gamma}
        
        return best_params
    
    def _admm_constrained_reconstruction(self, signal: np.ndarray, 
                                       phase_estimates: np.ndarray) -> np.ndarray:
        """ADMM-约束的稀疏重建"""
        # 应用相位补偿
        compensated_signal = self._apply_phase_compensation(signal, phase_estimates)
        
        # 执行ADMM稀疏重建
        sparse_result = self._perform_admm_reconstruction(compensated_signal)
        
        return sparse_result
    
    def _apply_phase_compensation(self, signal: np.ndarray, 
                                phase_estimates: np.ndarray) -> np.ndarray:
        """应用相位补偿"""
        num_range_bins, num_azimuth = signal.shape
        compensated_signal = np.zeros_like(signal)
        
        for r_idx in range(num_range_bins):
            compensation = np.exp(-1j * phase_estimates[r_idx, :])
            compensated_signal[r_idx, :] = signal[r_idx, :] * compensation
        
        return compensated_signal
    
    def _perform_admm_reconstruction(self, signal: np.ndarray) -> np.ndarray:
        """执行ADMM稀疏重建（简化实现）"""
        rho = self.params['admm']['rho']
        lambda_reg = self.params['admm']['lambda']
        max_iter = self.params['admm']['max_iter']
        tol = self.params['admm']['tol']
        
        # 初始化ADMM变量
        x = signal.copy()
        z = signal.copy()
        u = np.zeros_like(signal)
        
        # ADMM迭代
        for iteration in range(max_iter):
            # x-更新（数据保真度）
            x = (signal + rho * (z - u)) / (1 + rho)
            
            # z-更新（稀疏性）
            v = x + u
            z = self._soft_threshold(v, lambda_reg / rho)
            
            # u-更新（拉格朗日乘子）
            u = u + x - z
            
            # 检查收敛
            primal_residual = np.linalg.norm(x - z, 'fro')
            dual_residual = rho * np.linalg.norm(z - self._soft_threshold(v, lambda_reg / rho), 'fro')
            
            if primal_residual < tol and dual_residual < tol:
                break
        
        return x
    
    def _soft_threshold(self, x: np.ndarray, threshold: float) -> np.ndarray:
        """软阈值函数"""
        return np.sign(x) * np.maximum(np.abs(x) - threshold, 0)
    
    def _update_fusion_state(self, fusion_state: Dict, vmd_modes: np.ndarray,
                           phase_estimates: np.ndarray, sparse_result: np.ndarray) -> Tuple[Dict, bool]:
        """更新融合状态并检查收敛"""
        # 组合融合组件
        current_result = self._combine_fusion_components(vmd_modes, phase_estimates, sparse_result)
        
        # 检查收敛
        if fusion_state['iteration_count'] > 0:
            change = np.linalg.norm(current_result - fusion_state['current_signal'], 'fro') / \
                    np.linalg.norm(fusion_state['current_signal'], 'fro')
            converged = change < self.params['fusion']['convergence_tol']
            fusion_state['convergence_history'].append(change)
        else:
            converged = False
        
        # 更新状态
        fusion_state['current_signal'] = current_result
        fusion_state['iteration_count'] += 1
        
        return fusion_state, converged
    
    def _combine_fusion_components(self, vmd_modes: np.ndarray, phase_estimates: np.ndarray,
                                 sparse_result: np.ndarray) -> np.ndarray:
        """组合融合组件"""
        # 获取权重
        w_vmd = self.params['fusion']['vmd_weight']
        w_dcft = self.params['fusion']['dcft_weight']
        w_admm = self.params['fusion']['admm_weight']
        
        # 归一化权重
        total_weight = w_vmd + w_dcft + w_admm
        w_vmd /= total_weight
        w_dcft /= total_weight
        w_admm /= total_weight
        
        # 从VMD模态重建信号
        vmd_reconstructed = np.sum(vmd_modes, axis=0)
        
        # 加权组合
        combined_result = w_vmd * vmd_reconstructed + w_dcft * sparse_result + w_admm * sparse_result
        
        return combined_result
    
    def _generate_final_image(self, fusion_state: Dict) -> np.ndarray:
        """生成最终ISAR图像"""
        final_signal = fusion_state['current_signal']
        ISAR_image = fftshift(fft(final_signal, axis=1), axes=1)
        
        # 转换为dB
        ISAR_image = 20 * np.log10(np.abs(ISAR_image) / np.max(np.abs(ISAR_image)))
        
        return ISAR_image
    
    def _generate_processing_info(self, fusion_state: Dict) -> Dict:
        """生成处理信息"""
        final_image = np.abs(fusion_state['current_signal'])
        
        processing_info = {
            'iterations': fusion_state['iteration_count'],
            'convergence_history': fusion_state['convergence_history'],
            'image_contrast': np.std(final_image) / np.mean(final_image),
            'image_entropy': self._calculate_entropy(final_image),
            'sparsity_ratio': np.count_nonzero(np.abs(final_image) > 0.01 * np.max(np.abs(final_image))) / final_image.size
        }
        
        return processing_info
    
    def _calculate_entropy(self, image: np.ndarray) -> float:
        """计算图像熵"""
        normalized = image / np.sum(image)
        normalized[normalized == 0] = np.finfo(float).eps
        entropy = -np.sum(normalized * np.log2(normalized))
        return entropy
