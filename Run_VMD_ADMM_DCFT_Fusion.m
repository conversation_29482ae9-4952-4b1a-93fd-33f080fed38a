%% VMD-ADMM-DCFT融合ISAR成像算法测试脚本
%
% 本脚本演示了三技术有机融合的ISAR成像算法
% 包括性能对比和参数优化建议
%
% 作者: ISAR算法研究团队
% 日期: 2024

clear; close all; clc;

%% 1. 数据加载和准备
fprintf('=== VMD-ADMM-DCFT融合ISAR成像算法测试 ===\n\n');

% 尝试加载实际数据
data_files = {'shipx2.mat', 'ship_data.mat', 'radar_data.mat'};
data_loaded = false;

for i = 1:length(data_files)
    if exist(data_files{i}, 'file')
        fprintf('加载数据文件: %s\n', data_files{i});
        load(data_files{i});

        % 检查可能的变量名
        vars = who;
        data_var_names = {'shipx2', 'ship_data', 'radar_data', 'echo_data', 'data'};

        for j = 1:length(data_var_names)
            if ismember(data_var_names{j}, vars)
                eval(['echo_data = ' data_var_names{j} ';']);
                data_loaded = true;
                fprintf('使用变量: %s\n', data_var_names{j});
                break;
            end
        end

        if data_loaded
            break;
        end
    end
end

% 如果没有找到数据文件，生成仿真数据
if ~data_loaded
    fprintf('未找到数据文件，生成仿真舰船数据...\n');
    echo_data = generate_ship_simulation_data();
end

fprintf('数据尺寸: %d x %d\n', size(echo_data, 1), size(echo_data, 2));

%% 2. 参数配置
fprintf('\n=== 参数配置 ===\n');

% 基础参数
params = struct();

% VMD参数 - 针对舰船目标优化（性能优化版本）
params.vmd.K = 3;                    % 模态数量（减少以提高速度）
params.vmd.alpha = 2000;             % 平衡参数
params.vmd.tau = 0.1;                % 时间步长
params.vmd.tol = 1e-6;               % 收敛容限（放宽以提高速度）
params.vmd.max_iter = 200;           % 最大迭代次数（减少以提高速度）

% DCFT参数 - 三维运动补偿（优化搜索范围）
params.dcft.alpha_range = -32:16:320;    % 二阶啁啾率范围（步长加倍）
params.dcft.beta_range = -1000:400:2400; % 三阶啁啾率范围（步长加倍）
params.dcft.gamma_range = -500:200:1500; % 四阶啁啾率范围（步长加倍）

% ADMM参数 - 稀疏优化（快速收敛设置）
params.admm.rho = 1.0;               % 增广拉格朗日参数
params.admm.lambda = 0.1;            % 稀疏正则化参数
params.admm.max_iter = 50;           % 最大迭代次数（减少以提高速度）
params.admm.tol = 1e-5;              % 收敛容限（放宽以提高速度）

% 融合参数 - 协同优化（快速收敛设置）
params.fusion.max_global_iter = 3;    % 全局迭代次数（减少以提高速度）
params.fusion.convergence_tol = 1e-3; % 收敛容限（放宽以提高速度）
params.fusion.vmd_weight = 0.4;       % VMD权重
params.fusion.dcft_weight = 0.3;      % DCFT权重
params.fusion.admm_weight = 0.3;      % ADMM权重

% 雷达参数
params.radar.fc = 5.2e9;             % 载频 (Hz)
params.radar.B = 80e6;               % 带宽 (Hz)
params.radar.PRF = 1400;             % 脉冲重复频率 (Hz)

% 处理参数
params.processing.dynamic_range_db = 30; % 显示动态范围

fprintf('VMD模态数: %d\n', params.vmd.K);
fprintf('DCFT搜索范围: α[%d,%d], β[%d,%d], γ[%d,%d]\n', ...
    min(params.dcft.alpha_range), max(params.dcft.alpha_range), ...
    min(params.dcft.beta_range), max(params.dcft.beta_range), ...
    min(params.dcft.gamma_range), max(params.dcft.gamma_range));
fprintf('ADMM正则化参数: λ=%.3f, ρ=%.1f\n', params.admm.lambda, params.admm.rho);

% 计算预期计算复杂度
alpha_count = length(params.dcft.alpha_range);
beta_count = length(params.dcft.beta_range);
gamma_count = length(params.dcft.gamma_range);
total_dcft_searches = alpha_count * beta_count * gamma_count;
fprintf('DCFT搜索点数: %d (优化后，原始约为 %d)\n', total_dcft_searches, total_dcft_searches*16);

% 检查可用内存
try
    if ispc
        [~, sys_info] = memory;
        available_memory_gb = sys_info.PhysicalMemory.Available / 1024^3;
        fprintf('可用内存: %.1f GB\n', available_memory_gb);
    end
catch
    fprintf('无法获取内存信息\n');
end

% 检查并行处理能力
if exist('parfor', 'builtin')
    try
        pool = gcp('nocreate');
        if ~isempty(pool)
            fprintf('并行处理: 可用 (%d 工作进程)\n', pool.NumWorkers);
        else
            fprintf('并行处理: 可用但未启动\n');
        end
    catch
        fprintf('并行处理: 不可用\n');
    end
else
    fprintf('并行处理: 不支持\n');
end

%% 3. 执行融合算法
fprintf('\n=== 执行VMD-ADMM-DCFT融合算法 ===\n');

% 预估处理时间
estimated_time = estimate_processing_time(size(echo_data), params);
fprintf('预估处理时间: %.1f 秒\n', estimated_time);

% 启动性能监控
performance_timer = tic;
fprintf('开始处理...\n');

[ISAR_fusion, processing_info] = VMD_ADMM_DCFT_Fusion_ISAR(echo_data, params);
fusion_time = toc(performance_timer);

fprintf('实际处理时间: %.3f 秒 (预估: %.1f 秒)\n', fusion_time, estimated_time);
if fusion_time < estimated_time * 0.8
    fprintf('✅ 性能优化效果显著!\n');
elseif fusion_time < estimated_time * 1.2
    fprintf('✅ 处理时间在预期范围内\n');
else
    fprintf('⚠️  处理时间超出预期，建议进一步优化参数\n');
end

%% 4. 对比算法测试
fprintf('\n=== 对比算法测试 ===\n');

% 传统FFT成像
fprintf('执行传统FFT成像...\n');
tic;
ISAR_fft = 20*log10(abs(fftshift(fft(echo_data, [], 2), 2)) / max(abs(fftshift(fft(echo_data, [], 2), 2)), [], 'all'));
fft_time = toc;

% DCFT成像（如果存在）
if exist('DCFT_ISAR_Processor.m', 'file')
    fprintf('执行DCFT成像...\n');
    tic;
    [ISAR_dcft, ~] = DCFT_ISAR_Processor(echo_data, params);
    dcft_time = toc;
else
    fprintf('DCFT处理器不可用，跳过DCFT对比\n');
    ISAR_dcft = [];
    dcft_time = 0;
end

%% 5. 结果显示和分析
fprintf('\n=== 结果分析 ===\n');

% 显示成像结果
figure('Name', 'VMD-ADMM-DCFT融合ISAR成像结果对比', 'Position', [100, 100, 1200, 800]);

% 融合算法结果
subplot(2, 3, 1);
imagesc(ISAR_fusion);
colormap(jet); colorbar;
caxis([-params.processing.dynamic_range_db, 0]);
title('VMD-ADMM-DCFT融合成像');
xlabel('方位单元'); ylabel('距离单元');
axis xy;

% 传统FFT结果
subplot(2, 3, 2);
imagesc(ISAR_fft);
colormap(jet); colorbar;
caxis([-params.processing.dynamic_range_db, 0]);
title('传统FFT成像');
xlabel('方位单元'); ylabel('距离单元');
axis xy;

% DCFT结果（如果可用）
if ~isempty(ISAR_dcft)
    subplot(2, 3, 3);
    imagesc(ISAR_dcft);
    colormap(jet); colorbar;
    caxis([-params.processing.dynamic_range_db, 0]);
    title('DCFT成像');
    xlabel('方位单元'); ylabel('距离单元');
    axis xy;
end

% 收敛历史
if length(processing_info.convergence_history) > 1
    subplot(2, 3, 4);
    semilogy(processing_info.convergence_history, 'b-o', 'LineWidth', 2);
    grid on;
    title('融合算法收敛历史');
    xlabel('迭代次数'); ylabel('相对误差');
end

% 性能对比柱状图
subplot(2, 3, 5);
if ~isempty(ISAR_dcft)
    times = [fusion_time, fft_time, dcft_time];
    labels = {'融合算法', 'FFT', 'DCFT'};
else
    times = [fusion_time, fft_time];
    labels = {'融合算法', 'FFT'};
end
bar(times);
set(gca, 'XTickLabel', labels);
ylabel('处理时间 (秒)');
title('算法处理时间对比');
grid on;

% 图像质量指标对比
subplot(2, 3, 6);
contrast_fusion = processing_info.image_contrast;
entropy_fusion = processing_info.image_entropy;

% 计算FFT的质量指标
fft_image = abs(fftshift(fft(echo_data, [], 2), 2));
contrast_fft = std(fft_image(:)) / mean(fft_image(:));
entropy_fft = calculate_entropy(fft_image);

quality_metrics = [contrast_fusion, entropy_fusion; contrast_fft, entropy_fft];
if ~isempty(ISAR_dcft)
    dcft_image = 10.^(ISAR_dcft/20);
    contrast_dcft = std(dcft_image(:)) / mean(dcft_image(:));
    entropy_dcft = calculate_entropy(dcft_image);
    quality_metrics = [quality_metrics; contrast_dcft, entropy_dcft];
    legend_labels = {'融合算法', 'FFT', 'DCFT'};
else
    legend_labels = {'融合算法', 'FFT'};
end

bar(quality_metrics);
set(gca, 'XTickLabel', legend_labels);
legend({'对比度', '熵'}, 'Location', 'best');
ylabel('质量指标值');
title('图像质量指标对比');
grid on;

%% 6. 性能总结
fprintf('\n=== 性能总结 ===\n');
fprintf('融合算法处理时间: %.3f 秒\n', fusion_time);
fprintf('传统FFT处理时间: %.3f 秒\n', fft_time);
if dcft_time > 0
    fprintf('DCFT处理时间: %.3f 秒\n', dcft_time);
end
fprintf('\n图像质量指标:\n');
fprintf('融合算法 - 对比度: %.4f, 熵: %.4f\n', contrast_fusion, entropy_fusion);
fprintf('传统FFT - 对比度: %.4f, 熵: %.4f\n', contrast_fft, entropy_fft);
if ~isempty(ISAR_dcft)
    fprintf('DCFT算法 - 对比度: %.4f, 熵: %.4f\n', contrast_dcft, entropy_dcft);
end

fprintf('\n融合算法优势:\n');
fprintf('- 对比度提升: %.1f%%\n', (contrast_fusion - contrast_fft) / contrast_fft * 100);
fprintf('- 熵降低: %.1f%%\n', (entropy_fft - entropy_fusion) / entropy_fft * 100);
fprintf('- 迭代收敛: %d次\n', processing_info.iterations);
fprintf('- 稀疏度: %.3f\n', processing_info.sparsity_ratio);

%% 7. 保存结果
save_results = true;
if save_results
    save('VMD_ADMM_DCFT_Fusion_Results.mat', 'ISAR_fusion', 'processing_info', 'params', ...
         'ISAR_fft', 'fusion_time', 'fft_time');
    fprintf('\n结果已保存到: VMD_ADMM_DCFT_Fusion_Results.mat\n');
end

fprintf('\n=== 测试完成 ===\n');

%% ========== 辅助函数 ==========

function echo_data = generate_ship_simulation_data()
%生成仿真舰船数据

% 参数设置
num_range_bins = 64;
num_azimuth = 128;
fc = 5.2e9;
PRF = 1400;
c = 3e8;

% 生成时间向量
tm = (0:num_azimuth-1) / PRF;

% 舰船散射点模型
scatterers = [
    0, 0, 1.0;      % 舰船中心
    -20, 10, 0.8;   % 舰首
    20, -10, 0.7;   % 舰尾
    -10, 15, 0.6;   % 左舷
    10, -15, 0.6;   % 右舷
];

% 三维运动参数
omega_x = 0.1;  % 横摇角速度
omega_y = 0.05; % 纵摇角速度
omega_z = 0.02; % 偏航角速度

echo_data = zeros(num_range_bins, num_azimuth);

for i = 1:size(scatterers, 1)
    x0 = scatterers(i, 1);
    y0 = scatterers(i, 2);
    amp = scatterers(i, 3);

    % 三维运动建模
    x_t = x0 * cos(omega_z * tm) - y0 * sin(omega_z * tm);
    y_t = x0 * sin(omega_z * tm) + y0 * cos(omega_z * tm);

    % 距离历程
    R_t = sqrt(x_t.^2 + y_t.^2);

    % 相位历程（包含高阶项）
    phase = 4*pi*fc/c * R_t + ...
            2*pi * omega_x * tm.^2 + ...
            pi * omega_y * tm.^3 + ...
            0.5*pi * omega_z * tm.^4;

    % 生成回波信号
    signal = amp * exp(1j * phase);

    % 分配到距离单元
    range_bin = round(num_range_bins/2 + R_t(1)/10);
    range_bin = max(1, min(range_bin, num_range_bins));

    echo_data(range_bin, :) = echo_data(range_bin, :) + signal;
end

% 添加噪声
noise_power = 0.1;
noise = noise_power * (randn(size(echo_data)) + 1j * randn(size(echo_data)));
echo_data = echo_data + noise;

end

function entropy = calculate_entropy(image)
%计算图像熵

normalized = image / sum(image(:));
normalized(normalized == 0) = eps;
entropy = -sum(normalized(:) .* log2(normalized(:)));

end

function estimated_time = estimate_processing_time(data_size, params)
%估算处理时间

[num_range_bins, num_azimuth] = data_size;

% 基础时间估算（基于经验公式）
base_time_per_range_bin = 0.05; % 秒/距离单元（基础处理）

% VMD时间估算
vmd_time_factor = params.vmd.K * params.vmd.max_iter / 1000;
vmd_time = num_range_bins * num_azimuth * vmd_time_factor * 0.001;

% DCFT时间估算
dcft_searches = length(params.dcft.alpha_range) * ...
                length(params.dcft.beta_range) * ...
                length(params.dcft.gamma_range);
dcft_time_per_search = num_azimuth * 2e-6; % 秒/搜索点（包括FFT）
dcft_time = num_range_bins * dcft_searches * dcft_time_per_search;

% ADMM时间估算
admm_time_factor = params.admm.max_iter / 100;
admm_time = num_range_bins * num_azimuth * admm_time_factor * 0.0001;

% 融合迭代时间
fusion_iterations = params.fusion.max_global_iter;
single_iteration_time = vmd_time + dcft_time + admm_time;
total_time = single_iteration_time * fusion_iterations;

% 考虑并行处理加速
if exist('parfor', 'builtin')
    try
        pool = gcp('nocreate');
        if ~isempty(pool) && pool.NumWorkers > 1
            parallel_speedup = min(pool.NumWorkers, 4); % 最多4倍加速
            total_time = total_time / parallel_speedup;
        end
    catch
        % 如果无法获取并行池信息，不应用加速
    end
end

% 添加基础开销
estimated_time = total_time + base_time_per_range_bin * num_range_bins;

% 确保最小估算时间
estimated_time = max(estimated_time, 5); % 至少5秒

end
