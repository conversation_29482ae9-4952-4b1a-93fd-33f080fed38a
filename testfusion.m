% 加载舰船信号数据
load shipx2.mat; % 假设这是ISARrot_trans.m中生成的数据
Pos = [-10 -1 0;-9 -1 0;-8 -1 0;-7 -1 0;-6 -1 0;-5 -1 0;-3.75 -1 0;-3 -1 0;-2 -1 0;-1 -1 0;...   %(10个) 
       0 -1 0;...      %(1个)
       1 -1 0;2 -1 0;3 -1 0;4 -1 0;5 -1 0;6 -1 0;7 -1 0;8 -1 0;9 -1 0;10 -1 0;...  %(10个)
       -9.5 0.2 0.5;...    %(1个)
       -9.5 1.2 0.5;-9 1 0;-8 1 0;-7 1 0;-6 1 0;-5.2 1.2 0;-4.1 1 0;-3 1 0;-2 1 0;-1 1 0;...  %(10个)
       0 1 0;...      %(1个)
       1 1 0;2 1 0;3 1 0;4 1 0;5 1 0;6 1 0;7 1 0;8 1 0;9 1 0;10 1 0;...     %(10个)
       10.5 -0.75 0;10.5 0.75 0;11 -0.5 0;11 0.5 0;11.5 0 0;... %尾部        %(5个)
       9.5 0.5 0.5;9.5 -0.5 0.5;9 0 0.5;8.5 0.5 0.5;8.5 -0.5 0.5;... %头部 % (5个) 9.5 0 0.5;9 0.5 0.5;9 -0.5 0.5;8.5 0 0.5;
       5 0 0.5;5 0 1;5 0 1.5;5 0 2;5 0 2.5;5 0 3;5 0 3.5;5 0 4;...   %机头 % (8个)
       5.5 0.5 0.5;5.5 -0.5 0.5;4.5 0.5 0.5;4.5 -0.5 0.5;... %机头运动 % (4个)5 0 0.5; 5.5 0 0.5;5 0.5 0.5;5 -0.5 0.5; 4.5 0 0.5;  
       0.5 0.5 0.9;0.5 -0.5 0.9;-0.5 0.5 0.9;-0.5 -0.5 0.9;0 0 0.5;... %中间运动 % (5个) 0.5 0 1;-0.5 0 1;0 0.5 1;0 -0.5 1;
       -5 0 0.2;-5 0.2 0.8;-5 0.2 1.4;-5 0 2;... %机尾部 % (4个) 
       -5.5 -0.5 0.5;-5.5 0.5 0.5;-4.4 0.5 0.5;-4.5 -0.6 0.5;...%机尾运动 % (4个)-5 0 0.5;-5.5 0 0.5; -5 0.5 0.5;-5 -0.5 0.5;-4.5 0 0.5;
       ];

%-------------------------------------------------------------------------%
%-------------------------------    显示   -------------------------------%
x_Pos = Pos(:,1)*5;
y_Pos = Pos(:,2)*5;
z_Pos = Pos(:,3)*5;

figure;
plot3(x_Pos,y_Pos,z_Pos,'*');
grid on;
xlabel('X (m)'); ylabel('Y (m)'); zlabel('Z (m)');
title('目标散射点模型');

R = [cos(3*pi/8)*cos(0),cos(3*pi/8)*sin(0),sin(3*pi/8)];  %雷达目标视线单位矢量
Num_point = length(x_Pos); %目标点数
x_r = zeros(1,Num_point);
y_r = zeros(1,Num_point);
z_r = zeros(1,Num_point);
for n_point = 1:Num_point
    x_r(n_point) = y_Pos(n_point)*R(3)-z_Pos(n_point)*R(2);
    y_r(n_point) = z_Pos(n_point)*R(1)-x_Pos(n_point)*R(3);
    z_r(n_point) = x_Pos(n_point)*R(2)-y_Pos(n_point)*R(1);
end
x_oumiga = 0.05; %目标旋转初始角速度
y_oumiga = 0.2; %
z_oumiga = 0.05;
x_lamda = 0.05; %0.05%目标旋转角速度加速度
y_lamda = 0.1; %0.1
z_lamda = 0.05; %0.05
x_gamma = 0.05; %0.05%目标旋转角速度加加速度
y_gamma = 0.4; %0.4
z_gamma = 0.05; %0.05
f_v = zeros(1,Num_point); 
alpha_a = zeros(1,Num_point); 
beita_j = zeros(1,Num_point); 

for n_point = 1:Num_point
    f_v(n_point) = x_r(n_point)*x_oumiga+y_r(n_point)*y_oumiga+z_r(n_point)*z_oumiga;
    alpha_a(n_point) = x_r(n_point)*x_lamda+y_r(n_point)*y_lamda+z_r(n_point)*z_lamda;
    beita_j(n_point) = x_r(n_point)*x_gamma+y_r(n_point)*y_gamma+z_r(n_point)*z_gamma;
end
%---------------------------雷达系统参数与时域变量---------------------------%
B = 80*1e6;  %带宽
c = 3*1e8; 
PRF = 1400; %脉冲重复频率
fc = 5.2*1e9; %载频
delta_r = c/(2*B); %距离分辨率
r = -50*delta_r:delta_r:50*delta_r; %距离单元轴
% tm_orig = 0:(1/PRF):0.501; %慢时间轴 (原始，可能用于s_r_tm)

Num_r = length(r);
% Num_tm_orig = length(tm_orig); % 原始慢时间点数
ones_r = ones(1,Num_r);
% ones_tm_orig = ones(1,Num_tm_orig);


tm2 = 0:(1/PRF):0.501; % CVMD处理用的慢时间轴
Num_tm2 = length(tm2); % CVMD处理用的慢时间点数
ones_tm2 = ones(1,Num_tm2);
s_r_tm2 = 0;  % CVMD的输入回波 (Range compressed data)
Delta_R0 = zeros(1,Num_point); % 初始化

fprintf('正在生成ISAR回波数据 s_r_tm2...\n');
for n_point = 1:Num_point
    Delta_R0(n_point) = x_Pos(n_point)*R(1)+y_Pos(n_point)*R(2)+z_Pos(n_point)*R(3);%初始时刻的距离值

    Delta_R_t = Delta_R0(n_point) + f_v(n_point).*tm2 + (1/2)*alpha_a(n_point).*tm2.^2 + (1/6)*beita_j(n_point).*tm2.^3;
    sita_Delta_R_t = (4*pi*fc/c) * Delta_R_t;

    amplitude_factor = 1;
    if n_point>53 && n_point<62
        amplitude_factor = 1.3; 
    end
    if n_point == 48 
        amplitude_factor = amplitude_factor * 1; 
    end

    s_r_tm2 = s_r_tm2 + amplitude_factor * sinc((2*B/c)*(r.'*ones_tm2 - ones_r.'*Delta_R_t)) .* exp(1j*ones_r.'*sita_Delta_R_t);
end
shipx2=s_r_tm2;
% 设置参数
params = struct();
params.vmd.K = 3;
params.vmd.alpha = 2000;
params.vmd.tau = 0.1;
params.vmd.tol = 1e-7;
params.vmd.max_iter = 500;

params.dcft.alpha_step = 8;
params.dcft.alpha_min = -32;
params.dcft.alpha_max = 320;
params.dcft.beta_step = 100;
params.dcft.beta_min = -500;
params.dcft.beta_max = 2400;

params.admm.beta = 1000;
params.admm.gamma = 500;
params.admm.delta = 100;
params.admm.max_iter = 50;
params.admm.tol = 1e-5;

params.multiscale.window_sizes = [16, 32, 64];
params.multiscale.overlap = 0.5;

% 调用VMD-ADMM-DCFT算法
[ISAR_image_vmd_admm_dcft, s_compensated] = VMD_ADMM_DCFT_ISAR(shipx2, params);

% 计算传统FFT成像结果进行对比
traditional_image = fftshift(fft(shipx2, [], 2), 2);

% 显示对比结果
figure;
subplot(1,2,1);
imagesc(20*log10(abs(traditional_image)./max(abs(traditional_image(:)))));
caxis([-30, 0]);
colormap('jet'); colorbar;
title('传统FFT成像结果');
xlabel('方位单元'); ylabel('距离单元');
axis xy;

subplot(1,2,2);
imagesc(20*log10(abs(ISAR_image_vmd_admm_dcft)./max(abs(ISAR_image_vmd_admm_dcft(:)))));
caxis([-30, 0]);
colormap('jet'); colorbar;
title('VMD-ADMM-DCFT成像结果');
xlabel('方位单元'); ylabel('距离单元');
axis xy;

% 计算图像质量指标
contrast_traditional = contrast(traditional_image);
contrast_vmd_admm_dcft = contrast(ISAR_image_vmd_admm_dcft);

entropy_traditional = EntropyImage(abs(traditional_image)+eps);
entropy_vmd_admm_dcft = EntropyImage(abs(ISAR_image_vmd_admm_dcft)+eps);

fprintf('传统FFT成像:\n');
fprintf('  - 对比度: %.4f\n', contrast_traditional);
fprintf('  - 熵: %.4f\n', entropy_traditional);

fprintf('VMD-ADMM-DCFT成像:\n');
fprintf('  - 对比度: %.4f (提升: %.2f%%)\n', contrast_vmd_admm_dcft, 100*(contrast_vmd_admm_dcft-contrast_traditional)/contrast_traditional);
fprintf('  - 熵: %.4f (降低: %.2f%%)\n', entropy_vmd_admm_dcft, 100*(entropy_traditional-entropy_vmd_admm_dcft)/entropy_traditional);

function [ISAR_image_enhanced, s_compensated] = VMD_ADMM_DCFT_ISAR(radar_data, params)
% VMD_ADMM_DCFT_ISAR - 基于VMD-ADMM-DCFT联合优化的ISAR成像算法
%
% 该算法融合了三种关键技术：
% 1. VMD (变分模态分解) - 用于信号自适应分解
% 2. ADMM (交替方向乘数法) - 用于优化求解和稀疏约束
% 3. DCFT (离散立方傅里叶变换) - 用于处理非线性相位
%
% 输入:
%   radar_data - 距离压缩后的回波数据 (距离单元 x 方位单元)
%   params - 算法参数结构体
%
% 输出:
%   ISAR_image_enhanced - 增强型ISAR图像
%   s_compensated - 相位补偿后的信号

% 设置默认参数
if nargin < 2
    params = struct();
end

% VMD参数
if ~isfield(params, 'vmd')
    params.vmd = struct();
end
if ~isfield(params.vmd, 'K'), params.vmd.K = 3; end
if ~isfield(params.vmd, 'alpha'), params.vmd.alpha = 2000; end
if ~isfield(params.vmd, 'tau'), params.vmd.tau = 0.1; end
if ~isfield(params.vmd, 'tol'), params.vmd.tol = 1e-7; end
if ~isfield(params.vmd, 'max_iter'), params.vmd.max_iter = 500; end

% DCFT参数
if ~isfield(params, 'dcft')
    params.dcft = struct();
end
if ~isfield(params.dcft, 'alpha_step'), params.dcft.alpha_step = 8; end
if ~isfield(params.dcft, 'alpha_min'), params.dcft.alpha_min = -32; end
if ~isfield(params.dcft, 'alpha_max'), params.dcft.alpha_max = 320; end
if ~isfield(params.dcft, 'beta_step'), params.dcft.beta_step = 100; end
if ~isfield(params.dcft, 'beta_min'), params.dcft.beta_min = -500; end
if ~isfield(params.dcft, 'beta_max'), params.dcft.beta_max = 2400; end

% ADMM参数
if ~isfield(params, 'admm')
    params.admm = struct();
end
if ~isfield(params.admm, 'beta'), params.admm.beta = 1000; end
if ~isfield(params.admm, 'gamma'), params.admm.gamma = 500; end
if ~isfield(params.admm, 'delta'), params.admm.delta = 100; end
if ~isfield(params.admm, 'max_iter'), params.admm.max_iter = 50; end
if ~isfield(params.admm, 'tol'), params.admm.tol = 1e-5; end

% 多尺度参数
if ~isfield(params, 'multiscale')
    params.multiscale = struct();
end
if ~isfield(params.multiscale, 'window_sizes'), params.multiscale.window_sizes = [16, 32, 64]; end
if ~isfield(params.multiscale, 'overlap'), params.multiscale.overlap = 0.5; end

% 显示参数
if ~isfield(params, 'display'), params.display = true; end

% 获取数据尺寸
[N_r, N_tm] = size(radar_data);

% 创建慢时间轴（归一化）
tm_norm = (0:N_tm-1)/N_tm;
tm_indices = 0:N_tm-1;

% 频率轴（归一化频率，范围 [0, 1)）
omega_axis_norm = (0:N_tm-1) / N_tm;

% 初始化存储补偿后的信号
s_compensated = zeros(N_r, N_tm, 'like', radar_data);

if params.display
    fprintf('开始VMD-ADMM-DCFT ISAR处理 (共 %d 个距离单元)...\n', N_r);
end

% 对每个距离单元进行处理
for r_idx = 1:N_r
    if params.display && mod(r_idx, round(N_r/10)) == 0
        fprintf('处理距离单元: %d / %d\n', r_idx, N_r);
    end

    % 获取当前距离单元的信号
    signal_r_tm = radar_data(r_idx, :);
    
    % 跳过能量过低的距离单元
    signal_energy = sum(abs(signal_r_tm).^2);
    if signal_energy < 1e-10
        s_compensated(r_idx, :) = signal_r_tm;
        continue;
    end
    
    % ======== 多尺度VMD-DCFT分解 ========
    imfs_multiscale = cell(length(params.multiscale.window_sizes), 1);
    phase_params_multiscale = cell(length(params.multiscale.window_sizes), 1);
    scale_weights = zeros(length(params.multiscale.window_sizes), 1);
    
    for scale_idx = 1:length(params.multiscale.window_sizes)
        win_size = params.multiscale.window_sizes(scale_idx);
        
        % 确保窗口大小不超过信号长度
        if win_size > N_tm
            win_size = N_tm;
        end
        
        % 对当前尺度应用VMD-DCFT联合分解
        [imfs, phase_params, residual] = vmd_dcft_decompose(signal_r_tm, win_size, params);
        
        % 计算分解质量（例如，基于能量和相干性）
        imf_quality = calculate_decomposition_quality(imfs, phase_params, signal_r_tm);
        scale_weights(scale_idx) = imf_quality;
        
        % 存储当前尺度的结果
        imfs_multiscale{scale_idx} = imfs;
        phase_params_multiscale{scale_idx} = phase_params;
    end
    
    % 归一化权重
    scale_weights = scale_weights / sum(scale_weights);
    
    % ======== 基于ADMM的稀疏优化 ========
    % 初始化ADMM变量
    K = params.vmd.K; % 模态数量
    u_k_optimal = zeros(K, N_tm, 'like', 1j);
    z_k = zeros(K, N_tm, 'like', 1j);
    lambda_k = zeros(K, N_tm, 'like', 1j);
    
    % 初始化u_k为多尺度VMD的加权融合结果
    for k = 1:K
        for scale_idx = 1:length(params.multiscale.window_sizes)
            if size(imfs_multiscale{scale_idx}, 1) >= k
                u_k_optimal(k, :) = u_k_optimal(k, :) + scale_weights(scale_idx) * imfs_multiscale{scale_idx}(k, :);
            end
        end
    end
    
    % ADMM迭代求解
    for iter = 1:params.admm.max_iter
        % 保存上一次迭代结果
        u_k_prev = u_k_optimal;
        
        % 更新u_k（VMD部分）
        for k = 1:K
            % 计算除当前模态外的所有模态之和
            sum_other_modes = sum(u_k_optimal, 1) - u_k_optimal(k, :);
            
            % 基于VMD更新规则更新u_k
            u_k_optimal(k, :) = update_mode_vmd(signal_r_tm, sum_other_modes, z_k(k, :), lambda_k(k, :), params.vmd.alpha, omega_axis_norm);
        end
        
        % 更新z_k（DCFT相位拟合部分）
        for k = 1:K
            % 检查模态能量
            mode_energy = sum(abs(u_k_optimal(k, :)).^2);
            
            if mode_energy < 1e-10
                % 如果模态能量太低，跳过DCFT处理
                z_k(k, :) = u_k_optimal(k, :) + lambda_k(k, :) / params.admm.beta;
                continue;
            end
            
            % 使用DCFT估计和拟合相位参数
            try
                [a_k, b_k, c_k] = estimate_dcft_params(u_k_optimal(k, :), params.dcft);
                
                % 构建相位补偿函数
                phase_comp = exp(-1j * 2*pi * (a_k*tm_norm + (1/2)*b_k*tm_norm.^2 + (1/6)*c_k*tm_norm.^3));
                
                % 更新z_k，应用DCFT相位拟合
                z_k(k, :) = u_k_optimal(k, :) .* phase_comp + lambda_k(k, :) / params.admm.beta;
            catch
                % 如果DCFT估计失败，不进行相位补偿
                z_k(k, :) = u_k_optimal(k, :) + lambda_k(k, :) / params.admm.beta;
            end
            
            % 应用软阈值进行稀疏约束
            z_k(k, :) = soft_threshold(z_k(k, :), params.admm.delta/params.admm.beta);
        end
        
        % 更新拉格朗日乘子
        lambda_k = lambda_k + params.admm.beta * (u_k_optimal - z_k);
        
        % 检查收敛性
        diff_u = sum(sum(abs(u_k_optimal - u_k_prev).^2));
        norm_u = sum(sum(abs(u_k_prev).^2));
        
        if norm_u > 0 && diff_u/norm_u < params.admm.tol
            break;
        end
    end
    
    % ======== 相位误差估计与补偿 ========
    % 选择主导模态
    mode_energies = sum(abs(u_k_optimal).^2, 2);
    [max_energy, dominant_mode_idx] = max(mode_energies);
    
    % 检查是否有足够能量进行相位估计
    if max_energy < 1e-10
        % 如果没有足够能量，保持原始信号不变
        s_compensated(r_idx, :) = signal_r_tm;
        continue;
    end
    
    % 提取相位参数
    dominant_mode = u_k_optimal(dominant_mode_idx, :);
    
    % 使用DCFT估计主导模态的相位参数
    try
        [a_k, b_k, c_k] = estimate_dcft_params(dominant_mode, params.dcft);
        
        % 构建补偿函数
        phase_error = 2*pi * (a_k*tm_norm + (1/2)*b_k*tm_norm.^2 + (1/6)*c_k*tm_norm.^3);
        
        % 应用相位补偿
        s_compensated(r_idx, :) = signal_r_tm .* exp(-1j * phase_error);
    catch
        % 如果估计失败，保持原始信号不变
        s_compensated(r_idx, :) = signal_r_tm;
    end
end

if params.display
    fprintf('VMD-ADMM-DCFT ISAR处理完成。\n');
end

% ======== 生成增强型ISAR图像 ========
ISAR_image_enhanced = fftshift(fft(s_compensated, [], 2), 2);

% 显示结果
if params.display
    figure;
    ISAR_image_db = 20*log10(abs(ISAR_image_enhanced)./max(abs(ISAR_image_enhanced(:))));
    imagesc(ISAR_image_db);
    caxis([-30, 0]);
    colormap('jet'); colorbar;
    title('VMD-ADMM-DCFT 增强型ISAR成像结果 (dB)');
    xlabel('方位单元');
    ylabel('距离单元');
    axis xy;
end

end

% ======================== 辅助函数 ========================

function [imfs, phase_params, residual] = vmd_dcft_decompose(signal, window_length, params)
% 实现VMD-DCFT联合分解
% 输入:
%   signal - 输入信号
%   window_length - 窗口长度
%   params - 参数结构体
% 输出:
%   imfs - 分解后的IMFs
%   phase_params - 相位参数
%   residual - 残差

K = params.vmd.K;
alpha = params.vmd.alpha;
tau = params.vmd.tau;
tol = params.vmd.tol;
max_iter = params.vmd.max_iter;

N = length(signal);
signal = signal(:).'; % 确保为行向量

% 频率轴
omega = (0:N-1) / N;

% 初始化
u_k = zeros(K, N, 'like', 1j);
omega_k = zeros(K, 1);
lambda = zeros(1, N, 'like', 1j);

% 初始化中心频率
for k = 1:K
    omega_k(k) = (k-0.5) * (0.5/K);
end

% 信号频谱
signal_fft = fft(signal);

% VMD迭代
for iter = 1:max_iter
    % 保存上一次迭代结果
    u_k_prev = u_k;
    omega_k_prev = omega_k;
    
    % 更新每个模态
    for k = 1:K
        % 计算除当前模态外的所有模态之和
        sum_u_i = sum(u_k, 1) - u_k(k, :);
        
        % 更新u_k
        u_k_fft = (signal_fft - fft(sum_u_i) + fft(lambda)/2) ./ (1 + 2*alpha*(omega - omega_k(k)).^2);
        u_k(k, :) = ifft(u_k_fft);
        
        % 更新omega_k
        u_k_fft = fft(u_k(k, :));
        power_spectrum = abs(u_k_fft).^2;
        
        if sum(power_spectrum) > 0
            omega_k(k) = sum(omega .* power_spectrum) / sum(power_spectrum);
        end
    end
    
    % 更新拉格朗日乘子
    sum_u = sum(u_k, 1);
    lambda = lambda + tau * (signal - sum_u);
    
    % 检查收敛性
    if iter > 1
        u_diff = norm(u_k - u_k_prev, 'fro')^2 / norm(u_k_prev, 'fro')^2;
        omega_diff = norm(omega_k - omega_k_prev)^2 / norm(omega_k_prev)^2;
        
        if u_diff < tol && omega_diff < tol
            break;
        end
    end
end

% 使用DCFT估计每个IMF的相位参数
phase_params = zeros(K, 3); % [a_k, b_k, c_k] for each IMF
for k = 1:K
    % 检查IMF能量
    imf_energy = sum(abs(u_k(k, :)).^2);
    if imf_energy < 1e-10
        % 如果能量太低，设置默认参数
        phase_params(k, :) = [0, 0, 0];
        continue;
    end
    
    % 尝试估计相位参数
    try
        [a_k, b_k, c_k] = estimate_dcft_params(u_k(k, :), params.dcft);
        phase_params(k, :) = [a_k, b_k, c_k];
    catch
        % 如果估计失败，设置默认参数
        phase_params(k, :) = [0, 0, 0];
    end
end

% 计算残差
residual = signal - sum(u_k, 1);

% 返回IMFs和相位参数
imfs = u_k;
end

function [a_k, b_k, c_k] = estimate_dcft_params(signal, dcft_params)
% 使用DCFT估计信号的相位参数
% 输入:
%   signal - 输入信号
%   dcft_params - DCFT参数
% 输出:
%   a_k - 线性相位项系数
%   b_k - 二次相位项系数
%   c_k - 三次相位项系数

N = length(signal);
tm = (0:N-1) / N;

% 定义搜索范围
alpha_values = dcft_params.alpha_min:dcft_params.alpha_step:dcft_params.alpha_max;
beta_values = dcft_params.beta_min:dcft_params.beta_step:dcft_params.beta_max;

% 初始化输出参数的默认值
a_k = 0;
b_k = 0;
c_k = 0;

% 初始化最大响应
max_response = 0;
best_alpha = 0;
best_beta = 0;

% 如果信号能量过低，直接返回默认值
if sum(abs(signal).^2) < 1e-10
    return;
end

% 搜索最佳参数
for alpha = alpha_values
    for beta = beta_values
        % 构建去啁啾项
        comp_phase = -1j * 2*pi * ((1/2)*alpha*tm.*tm + (1/6)*beta*tm.*tm.*tm);
        compensation = exp(comp_phase);
        
        % 应用去啁啾
        dechirped = signal .* compensation;
        
        % FFT处理
        spectrum = fft(dechirped);
        
        % 计算响应
        current_max = max(abs(spectrum));
        
        % 更新最大响应
        if current_max > max_response
            max_response = current_max;
            best_alpha = alpha;
            best_beta = beta;
            
            % 找到主频率分量
            [~, max_idx] = max(abs(spectrum));
            if max_idx > N/2
                max_idx = max_idx - N;
            end
            a_k = max_idx / N; % 归一化频率
        end
    end
end

% 返回估计的参数
b_k = best_alpha;
c_k = best_beta;

% 即使在无法找到有效参数的情况下，也确保返回合理的默认值
if max_response == 0
    % 估计可能失败，使用信号的直接频谱估计线性相位项
    spectrum = fft(signal);
    [~, max_idx] = max(abs(spectrum));
    if max_idx > N/2
        max_idx = max_idx - N;
    end
    a_k = max_idx / N;
end
end

function quality = calculate_decomposition_quality(imfs, phase_params, original_signal)
% 计算分解质量
% 输入:
%   imfs - 分解后的IMFs
%   phase_params - 相位参数
%   original_signal - 原始信号
% 输出:
%   quality - 分解质量指标

% 计算重构信号
reconstructed_signal = sum(imfs, 1);

% 计算重构误差
reconstruction_error = norm(original_signal - reconstructed_signal)^2 / norm(original_signal)^2;

% 计算模态能量
mode_energies = sum(abs(imfs).^2, 2);
total_energy = sum(mode_energies);

% 计算分解质量（考虑重构误差和能量分布）
if total_energy > 0
    energy_concentration = max(mode_energies) / total_energy;
else
    energy_concentration = 0;
end

% 综合质量指标
quality = energy_concentration * (1 - reconstruction_error);

% 确保质量指标为正值
quality = max(0, quality);
end

function u_k_updated = update_mode_vmd(signal, sum_other_modes, z_k, lambda_k, alpha, omega)
% 基于VMD规则更新模态
% 输入:
%   signal - 原始信号
%   sum_other_modes - 其他模态之和
%   z_k - 辅助变量
%   lambda_k - 拉格朗日乘子
%   alpha - VMD平衡参数
%   omega - 频率轴
% 输出:
%   u_k_updated - 更新后的模态

% 计算信号减去其他模态
residual = signal - sum_other_modes;

% 傅里叶变换
residual_fft = fft(residual);
z_k_fft = fft(z_k);
lambda_k_fft = fft(lambda_k);

% 中心频率（使用z_k的主频率）
[~, max_idx] = max(abs(z_k_fft));
N = length(omega);
if max_idx > N/2
    max_idx = max_idx - N;
end
omega_k = max_idx / N;

% 更新u_k的频谱
u_k_fft = (residual_fft + z_k_fft - lambda_k_fft/2) ./ (1 + alpha*(omega - omega_k).^2);

% 逆傅里叶变换
u_k_updated = ifft(u_k_fft);
end

function y = soft_threshold(x, lambda)
% 软阈值函数
% 输入:
%   x - 输入信号
%   lambda - 阈值参数
% 输出:
%   y - 阈值处理后的信号

abs_x = abs(x);
phase_x = angle(x);

% 软阈值
magnitude = max(abs_x - lambda, 0);

% 重建信号
y = magnitude .* exp(1j * phase_x);
end