"""
VMD-ADMM-DCFT融合ISAR成像算法测试脚本

本脚本测试三技术融合算法的性能，并与传统方法进行对比。

作者: ISAR算法研究团队
日期: 2024
"""

import numpy as np
import matplotlib.pyplot as plt
import time
from scipy.fft import fft, fftshift
import sys
import os

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from vmd_admm_dcft_fusion import VMD_ADMM_DCFT_Fusion

def generate_ship_simulation_data():
    """生成仿真舰船数据"""
    # 参数设置
    num_range_bins = 64
    num_azimuth = 128
    fc = 5.2e9
    PRF = 1400
    c = 3e8
    
    # 生成时间向量
    tm = np.arange(num_azimuth) / PRF
    
    # 舰船散射点模型
    scatterers = np.array([
        [0, 0, 1.0],      # 舰船中心
        [-20, 10, 0.8],   # 舰首
        [20, -10, 0.7],   # 舰尾
        [-10, 15, 0.6],   # 左舷
        [10, -15, 0.6],   # 右舷
    ])
    
    # 三维运动参数
    omega_x = 0.1   # 横摇角速度
    omega_y = 0.05  # 纵摇角速度
    omega_z = 0.02  # 偏航角速度
    
    echo_data = np.zeros((num_range_bins, num_azimuth), dtype=complex)
    
    for i, (x0, y0, amp) in enumerate(scatterers):
        # 三维运动建模
        x_t = x0 * np.cos(omega_z * tm) - y0 * np.sin(omega_z * tm)
        y_t = x0 * np.sin(omega_z * tm) + y0 * np.cos(omega_z * tm)
        
        # 距离历程
        R_t = np.sqrt(x_t**2 + y_t**2)
        
        # 相位历程（包含高阶项）
        phase = (4*np.pi*fc/c * R_t + 
                2*np.pi * omega_x * tm**2 + 
                np.pi * omega_y * tm**3 + 
                0.5*np.pi * omega_z * tm**4)
        
        # 生成回波信号
        signal = amp * np.exp(1j * phase)
        
        # 分配到距离单元
        range_bin = int(num_range_bins/2 + R_t[0]/10)
        range_bin = max(0, min(range_bin, num_range_bins-1))
        
        echo_data[range_bin, :] += signal
    
    # 添加噪声
    noise_power = 0.1
    noise = noise_power * (np.random.randn(*echo_data.shape) + 
                          1j * np.random.randn(*echo_data.shape))
    echo_data += noise
    
    return echo_data

def calculate_image_quality_metrics(image):
    """计算图像质量指标"""
    image_abs = np.abs(image)
    
    # 对比度
    contrast = np.std(image_abs) / np.mean(image_abs)
    
    # 熵
    normalized = image_abs / np.sum(image_abs)
    normalized[normalized == 0] = np.finfo(float).eps
    entropy = -np.sum(normalized * np.log2(normalized))
    
    # 聚焦度（基于梯度）
    grad_x = np.gradient(image_abs, axis=1)
    grad_y = np.gradient(image_abs, axis=0)
    focus = np.mean(np.sqrt(grad_x**2 + grad_y**2))
    
    return {
        'contrast': contrast,
        'entropy': entropy,
        'focus': focus
    }

def main():
    """主测试函数"""
    print("=== VMD-ADMM-DCFT融合ISAR成像算法测试 ===\n")
    
    # 1. 生成测试数据
    print("1. 生成仿真舰船数据...")
    echo_data = generate_ship_simulation_data()
    print(f"数据尺寸: {echo_data.shape[0]} x {echo_data.shape[1]}")
    
    # 2. 配置参数
    print("\n2. 配置算法参数...")
    params = {
        'vmd': {
            'K': 4,
            'alpha': 2000,
            'tau': 0.1,
            'tol': 1e-7,
            'max_iter': 200  # 减少迭代次数以提高测试速度
        },
        'dcft': {
            'alpha_range': np.arange(-32, 321, 16),  # 降低搜索密度
            'beta_range': np.arange(-1000, 2401, 400),
            'gamma_range': np.arange(-500, 1501, 200)
        },
        'admm': {
            'rho': 1.0,
            'lambda': 0.1,
            'max_iter': 50,  # 减少迭代次数
            'tol': 1e-6
        },
        'fusion': {
            'max_global_iter': 3,  # 减少全局迭代次数
            'convergence_tol': 1e-4,
            'vmd_weight': 0.4,
            'dcft_weight': 0.3,
            'admm_weight': 0.3
        },
        'radar': {
            'fc': 5.2e9,
            'B': 80e6,
            'PRF': 1400
        }
    }
    
    print(f"VMD模态数: {params['vmd']['K']}")
    print(f"全局迭代次数: {params['fusion']['max_global_iter']}")
    
    # 3. 执行融合算法
    print("\n3. 执行VMD-ADMM-DCFT融合算法...")
    fusion_processor = VMD_ADMM_DCFT_Fusion(params)
    
    start_time = time.time()
    ISAR_fusion, processing_info = fusion_processor.process(echo_data)
    fusion_time = time.time() - start_time
    
    print(f"融合算法处理时间: {fusion_time:.3f} 秒")
    print(f"迭代次数: {processing_info['iterations']}")
    
    # 4. 传统FFT对比
    print("\n4. 执行传统FFT成像...")
    start_time = time.time()
    ISAR_fft = fftshift(fft(echo_data, axis=1), axes=1)
    ISAR_fft_db = 20 * np.log10(np.abs(ISAR_fft) / np.max(np.abs(ISAR_fft)))
    fft_time = time.time() - start_time
    
    print(f"FFT处理时间: {fft_time:.3f} 秒")
    
    # 5. 质量指标计算
    print("\n5. 计算图像质量指标...")
    
    # 融合算法质量指标
    fusion_metrics = calculate_image_quality_metrics(10**(ISAR_fusion/20))
    
    # FFT质量指标
    fft_metrics = calculate_image_quality_metrics(10**(ISAR_fft_db/20))
    
    print("\n质量指标对比:")
    print(f"{'指标':<10} {'融合算法':<12} {'传统FFT':<12} {'改进幅度':<10}")
    print("-" * 50)
    
    contrast_improvement = (fusion_metrics['contrast'] - fft_metrics['contrast']) / fft_metrics['contrast'] * 100
    entropy_improvement = (fft_metrics['entropy'] - fusion_metrics['entropy']) / fft_metrics['entropy'] * 100
    focus_improvement = (fusion_metrics['focus'] - fft_metrics['focus']) / fft_metrics['focus'] * 100
    
    print(f"{'对比度':<10} {fusion_metrics['contrast']:<12.4f} {fft_metrics['contrast']:<12.4f} {contrast_improvement:>+7.1f}%")
    print(f"{'熵':<10} {fusion_metrics['entropy']:<12.4f} {fft_metrics['entropy']:<12.4f} {entropy_improvement:>+7.1f}%")
    print(f"{'聚焦度':<10} {fusion_metrics['focus']:<12.4f} {fft_metrics['focus']:<12.4f} {focus_improvement:>+7.1f}%")
    
    # 6. 结果可视化
    print("\n6. 生成结果图像...")
    
    plt.figure(figsize=(15, 10))
    
    # 融合算法结果
    plt.subplot(2, 3, 1)
    plt.imshow(ISAR_fusion, aspect='auto', cmap='jet', vmin=-30, vmax=0)
    plt.colorbar(label='幅度 (dB)')
    plt.title('VMD-ADMM-DCFT融合成像')
    plt.xlabel('方位单元')
    plt.ylabel('距离单元')
    
    # 传统FFT结果
    plt.subplot(2, 3, 2)
    plt.imshow(ISAR_fft_db, aspect='auto', cmap='jet', vmin=-30, vmax=0)
    plt.colorbar(label='幅度 (dB)')
    plt.title('传统FFT成像')
    plt.xlabel('方位单元')
    plt.ylabel('距离单元')
    
    # 差异图
    plt.subplot(2, 3, 3)
    difference = ISAR_fusion - ISAR_fft_db
    plt.imshow(difference, aspect='auto', cmap='RdBu_r')
    plt.colorbar(label='差异 (dB)')
    plt.title('融合算法 - FFT差异')
    plt.xlabel('方位单元')
    plt.ylabel('距离单元')
    
    # 收敛历史
    if len(processing_info['convergence_history']) > 1:
        plt.subplot(2, 3, 4)
        plt.semilogy(processing_info['convergence_history'], 'b-o', linewidth=2)
        plt.grid(True)
        plt.title('融合算法收敛历史')
        plt.xlabel('迭代次数')
        plt.ylabel('相对误差')
    
    # 处理时间对比
    plt.subplot(2, 3, 5)
    times = [fusion_time, fft_time]
    labels = ['融合算法', 'FFT']
    bars = plt.bar(labels, times, color=['blue', 'orange'])
    plt.ylabel('处理时间 (秒)')
    plt.title('算法处理时间对比')
    plt.grid(True, alpha=0.3)
    
    # 在柱状图上添加数值
    for bar, time_val in zip(bars, times):
        plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                f'{time_val:.3f}s', ha='center', va='bottom')
    
    # 质量指标对比
    plt.subplot(2, 3, 6)
    metrics_names = ['对比度', '熵', '聚焦度']
    fusion_values = [fusion_metrics['contrast'], fusion_metrics['entropy'], fusion_metrics['focus']]
    fft_values = [fft_metrics['contrast'], fft_metrics['entropy'], fft_metrics['focus']]
    
    x = np.arange(len(metrics_names))
    width = 0.35
    
    plt.bar(x - width/2, fusion_values, width, label='融合算法', color='blue', alpha=0.7)
    plt.bar(x + width/2, fft_values, width, label='传统FFT', color='orange', alpha=0.7)
    
    plt.xlabel('质量指标')
    plt.ylabel('指标值')
    plt.title('图像质量指标对比')
    plt.xticks(x, metrics_names)
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.show()
    
    # 7. 总结
    print("\n=== 测试总结 ===")
    print(f"融合算法在对比度上提升了 {contrast_improvement:.1f}%")
    print(f"融合算法在熵上改善了 {entropy_improvement:.1f}%")
    print(f"融合算法在聚焦度上提升了 {focus_improvement:.1f}%")
    print(f"处理时间比: {fusion_time/fft_time:.1f}x")
    
    if contrast_improvement > 0 and entropy_improvement > 0:
        print("\n✅ 融合算法在图像质量上显著优于传统FFT方法")
    else:
        print("\n⚠️  融合算法需要进一步优化参数")
    
    print("\n算法特点:")
    print("- 有机融合VMD、ADMM、DCFT三种技术")
    print("- 联合迭代优化框架")
    print("- 自适应参数调整机制")
    print("- 适用于复杂运动目标成像")

if __name__ == "__main__":
    # 设置随机种子以确保结果可重现
    np.random.seed(42)
    
    try:
        main()
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"\n测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
