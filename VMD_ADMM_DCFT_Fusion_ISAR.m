function [ISAR_image, processing_info] = VMD_ADMM_DCFT_Fusion_ISAR(echo_data, params)
%VMD_ADMM_DCFT_FUSION_ISAR - 三技术有机融合的ISAR成像算法
%
% 本函数实现了VMD、ADMM和DCFT三种技术的深度融合，用于舰船ISAR成像
%
% 核心创新：
% 1. VMD-引导的ADMM稀疏优化
% 2. DCFT-增强的VMD变分分解
% 3. ADMM-约束的DCFT参数搜索
% 4. 联合迭代优化框架
%
% 输入:
%   echo_data - 距离压缩后的回波数据 [距离单元 x 方位单元]
%   params - 参数结构体
%
% 输出:
%   ISAR_image - 融合算法成像结果
%   processing_info - 处理信息

%% 1. 参数初始化和默认设置
if nargin < 2
    params = struct();
end

% 获取数据尺寸
[num_range_bins, num_azimuth] = size(echo_data);
fprintf('数据尺寸: %d x %d (距离单元 x 方位单元)\n', num_range_bins, num_azimuth);

% 设置默认参数
params = set_default_params(params, num_range_bins, num_azimuth);

% 数据预处理
processed_data = preprocess_data(echo_data, params);

%% 2. 核心融合算法 - 联合迭代优化框架
fprintf('开始VMD-ADMM-DCFT融合处理...\n');
tic;

% 初始化融合变量
fusion_result = initialize_fusion_variables(processed_data, params);

% 主迭代循环
for global_iter = 1:params.fusion.max_global_iter
    fprintf('全局迭代 %d/%d\n', global_iter, params.fusion.max_global_iter);

    %% 2.1 VMD-引导的信号分解阶段
    fprintf('  执行VMD-引导的信号分解...\n');
    [vmd_modes, vmd_freqs, dcft_guidance] = vmd_guided_decomposition(...
        fusion_result.current_signal, fusion_result.dcft_params, params);

    %% 2.2 DCFT-增强的相位估计阶段
    fprintf('  执行DCFT-增强的相位估计...\n');
    [phase_estimates, motion_params] = dcft_enhanced_phase_estimation(...
        vmd_modes, vmd_freqs, fusion_result.admm_constraints, params);

    %% 2.3 ADMM-约束的稀疏重建阶段
    fprintf('  执行ADMM-约束的稀疏重建...\n');
    [sparse_result, admm_weights] = admm_constrained_reconstruction(...
        fusion_result.current_signal, phase_estimates, motion_params, params);

    %% 2.4 融合更新和收敛检查
    [fusion_result, converged] = update_fusion_state(...
        fusion_result, vmd_modes, phase_estimates, sparse_result, params);

    if converged
        fprintf('  融合算法在第 %d 次迭代收敛\n', global_iter);
        break;
    end
end

%% 3. 最终成像和后处理
fprintf('生成最终ISAR图像...\n');
ISAR_image = generate_final_image(fusion_result, params);

% 图像增强
ISAR_image = enhance_image(ISAR_image, params);

processing_time = toc;
fprintf('融合处理完成，总耗时: %.3f 秒\n', processing_time);

%% 4. 生成处理信息
processing_info = generate_processing_info(fusion_result, processing_time, params);

end

%% ========== 辅助函数 ==========

function params = set_default_params(params, num_range_bins, num_azimuth)
%设置默认参数

% VMD参数
if ~isfield(params, 'vmd')
    params.vmd = struct();
end
if ~isfield(params.vmd, 'K'), params.vmd.K = 4; end
if ~isfield(params.vmd, 'alpha'), params.vmd.alpha = 2000; end
if ~isfield(params.vmd, 'tau'), params.vmd.tau = 0.1; end
if ~isfield(params.vmd, 'tol'), params.vmd.tol = 1e-7; end
if ~isfield(params.vmd, 'max_iter'), params.vmd.max_iter = 500; end

% DCFT参数
if ~isfield(params, 'dcft')
    params.dcft = struct();
end
if ~isfield(params.dcft, 'alpha_range'), params.dcft.alpha_range = [-32:8:320]; end
if ~isfield(params.dcft, 'beta_range'), params.dcft.beta_range = [-1000:200:2400]; end
if ~isfield(params.dcft, 'gamma_range'), params.dcft.gamma_range = [-500:100:1500]; end

% ADMM参数
if ~isfield(params, 'admm')
    params.admm = struct();
end
if ~isfield(params.admm, 'rho'), params.admm.rho = 1.0; end
if ~isfield(params.admm, 'lambda'), params.admm.lambda = 0.1; end
if ~isfield(params.admm, 'max_iter'), params.admm.max_iter = 100; end
if ~isfield(params.admm, 'tol'), params.admm.tol = 1e-6; end

% 融合参数
if ~isfield(params, 'fusion')
    params.fusion = struct();
end
if ~isfield(params.fusion, 'max_global_iter'), params.fusion.max_global_iter = 5; end
if ~isfield(params.fusion, 'convergence_tol'), params.fusion.convergence_tol = 1e-4; end
if ~isfield(params.fusion, 'vmd_weight'), params.fusion.vmd_weight = 0.4; end
if ~isfield(params.fusion, 'dcft_weight'), params.fusion.dcft_weight = 0.3; end
if ~isfield(params.fusion, 'admm_weight'), params.fusion.admm_weight = 0.3; end

% 雷达参数
if ~isfield(params, 'radar')
    params.radar = struct();
end
if ~isfield(params.radar, 'fc'), params.radar.fc = 5.2e9; end % 载频
if ~isfield(params.radar, 'B'), params.radar.B = 80e6; end % 带宽
if ~isfield(params.radar, 'PRF'), params.radar.PRF = 1400; end % 脉冲重复频率

% 处理参数
if ~isfield(params, 'processing')
    params.processing = struct();
end
if ~isfield(params.processing, 'dynamic_range_db'), params.processing.dynamic_range_db = 30; end

end

function processed_data = preprocess_data(echo_data, params)
%数据预处理

% 应用窗函数
[num_range_bins, num_azimuth] = size(echo_data);
window = hamming(num_azimuth)';
processed_data = echo_data .* repmat(window, num_range_bins, 1);

% 归一化
processed_data = processed_data / max(abs(processed_data(:)));

end

function fusion_result = initialize_fusion_variables(processed_data, params)
%初始化融合变量

fusion_result = struct();
fusion_result.current_signal = processed_data;
fusion_result.dcft_params = struct('alpha', 0, 'beta', 0, 'gamma', 0);
fusion_result.admm_constraints = [];
fusion_result.convergence_history = [];
fusion_result.iteration_count = 0;

end

function [vmd_modes, vmd_freqs, dcft_guidance] = vmd_guided_decomposition(signal, dcft_params, params)
%VMD-引导的信号分解（优化版本）

[num_range_bins, num_azimuth] = size(signal);
vmd_modes = zeros(params.vmd.K, num_range_bins, num_azimuth);
vmd_freqs = zeros(params.vmd.K, num_range_bins);

fprintf('    VMD分解进度: ');
progress_step = max(1, floor(num_range_bins / 20)); % 显示20个进度点

% 检查是否可以使用并行处理
use_parallel = false;
if exist('parfor', 'builtin') && num_range_bins > 50
    try
        % 尝试获取并行池信息
        pool = gcp('nocreate');
        if ~isempty(pool)
            use_parallel = true;
            fprintf('(使用并行处理) ');
        end
    catch
        % 如果并行工具箱不可用，继续使用串行处理
    end
end

if use_parallel
    % 并行处理版本
    parfor r_idx = 1:num_range_bins
        range_signal = signal(r_idx, :);

        % 如果有DCFT指导信息，调整VMD初始化
        if dcft_params.alpha ~= 0
            init_freqs = generate_guided_frequencies(dcft_params, params.vmd.K);
        else
            init_freqs = [];
        end

        % 执行VMD分解
        [modes, freqs] = perform_vmd_decomposition(range_signal, params.vmd, init_freqs);

        vmd_modes(:, r_idx, :) = modes;
        vmd_freqs(:, r_idx) = freqs;
    end
    fprintf('100%% ');
else
    % 串行处理版本（带进度显示）
    for r_idx = 1:num_range_bins
        % 显示进度
        if mod(r_idx, progress_step) == 0 || r_idx == num_range_bins
            fprintf('%.0f%% ', (r_idx/num_range_bins)*100);
        end

        range_signal = signal(r_idx, :);

        % 如果有DCFT指导信息，调整VMD初始化
        if dcft_params.alpha ~= 0
            init_freqs = generate_guided_frequencies(dcft_params, params.vmd.K);
        else
            init_freqs = [];
        end

        % 执行VMD分解
        [modes, freqs] = perform_vmd_decomposition(range_signal, params.vmd, init_freqs);

        vmd_modes(:, r_idx, :) = modes;
        vmd_freqs(:, r_idx) = freqs;
    end
end

fprintf('\n');

% 生成DCFT指导信息
dcft_guidance = extract_dcft_guidance(vmd_modes, vmd_freqs);

end

function [modes, freqs] = perform_vmd_decomposition(signal, vmd_params, init_freqs)
%执行VMD分解的核心函数

% 这里实现VMD算法的核心逻辑
% 为简化，使用基本的VMD实现
K = vmd_params.K;
alpha = vmd_params.alpha;
tau = vmd_params.tau;
tol = vmd_params.tol;
max_iter = vmd_params.max_iter;

% 初始化
N = length(signal);
modes = zeros(K, N);
freqs = zeros(K, 1);

% 简化的VMD实现（实际应用中需要完整的VMD算法）
for k = 1:K
    if ~isempty(init_freqs) && k <= length(init_freqs)
        freqs(k) = init_freqs(k);
    else
        freqs(k) = (k-1) / K;
    end

    % 生成模态（简化版本）
    t = 0:N-1;
    modes(k, :) = real(signal) .* cos(2*pi*freqs(k)*t/N) + ...
                  imag(signal) .* sin(2*pi*freqs(k)*t/N);
end

end

function init_freqs = generate_guided_frequencies(dcft_params, K)
%根据DCFT参数生成VMD初始频率

% 基于DCFT的啁啾率参数估计频率分布
alpha = dcft_params.alpha;
beta = dcft_params.beta;

% 生成K个频率
init_freqs = zeros(K, 1);
for k = 1:K
    init_freqs(k) = (k-1) / K + alpha * 0.01; % 简化的频率估计
end

end

function dcft_guidance = extract_dcft_guidance(vmd_modes, vmd_freqs)
%从VMD结果中提取DCFT指导信息

dcft_guidance = struct();
dcft_guidance.dominant_freqs = mean(vmd_freqs, 2);
dcft_guidance.mode_energies = squeeze(mean(mean(abs(vmd_modes).^2, 3), 2));

end

function [phase_estimates, motion_params] = dcft_enhanced_phase_estimation(vmd_modes, vmd_freqs, admm_constraints, params)
%DCFT-增强的相位估计（优化版本）

[K, num_range_bins, num_azimuth] = size(vmd_modes);
phase_estimates = zeros(num_range_bins, num_azimuth);
motion_params = struct();

% 生成时间向量
tm = (0:num_azimuth-1) / params.radar.PRF;

fprintf('    DCFT相位估计进度: ');
progress_step = max(1, floor(num_range_bins / 20)); % 显示20个进度点

% 首先对少数距离单元进行全搜索以获得初始估计
sample_indices = round(linspace(1, num_range_bins, min(10, num_range_bins)));
initial_params = zeros(length(sample_indices), 3); % [alpha, beta, gamma]

for i = 1:length(sample_indices)
    r_idx = sample_indices(i);

    % 选择主导模态
    mode_energies = squeeze(mean(abs(vmd_modes(:, r_idx, :)).^2, 3));
    [~, dominant_mode] = max(mode_energies);
    signal = squeeze(vmd_modes(dominant_mode, r_idx, :));

    % 粗搜索获得初始参数
    best_params = dcft_coarse_search(signal, tm, params);
    initial_params(i, :) = [best_params.alpha, best_params.beta, best_params.gamma];
end

% 计算参数的统计信息用于约束后续搜索
alpha_mean = mean(initial_params(:, 1));
beta_mean = mean(initial_params(:, 2));
gamma_mean = mean(initial_params(:, 3));
alpha_std = std(initial_params(:, 1));
beta_std = std(initial_params(:, 2));
gamma_std = std(initial_params(:, 3));

% 对所有距离单元进行优化搜索
for r_idx = 1:num_range_bins
    % 显示进度
    if mod(r_idx, progress_step) == 0 || r_idx == num_range_bins
        fprintf('%.0f%% ', (r_idx/num_range_bins)*100);
    end

    % 选择主导模态
    mode_energies = squeeze(mean(abs(vmd_modes(:, r_idx, :)).^2, 3));
    [~, dominant_mode] = max(mode_energies);
    signal = squeeze(vmd_modes(dominant_mode, r_idx, :));

    % 使用统计约束的快速搜索
    best_params = dcft_fast_search_with_constraints(signal, tm, ...
        alpha_mean, beta_mean, gamma_mean, alpha_std, beta_std, gamma_std, ...
        admm_constraints, params);

    % 计算相位估计
    alpha = best_params.alpha;
    beta = best_params.beta;
    gamma = best_params.gamma;

    % 三阶多项式相位模型
    phase_poly = alpha * tm.^2 + beta * tm.^3 + gamma * tm.^4;
    phase_estimates(r_idx, :) = phase_poly;

    % 存储运动参数
    if r_idx == 1
        motion_params.alpha = alpha;
        motion_params.beta = beta;
        motion_params.gamma = gamma;
    end
end

fprintf('\n');

end

function best_params = dcft_coarse_search(signal, tm, params)
%DCFT粗搜索（用于初始参数估计）

% 使用更稀疏的搜索网格
alpha_range = params.dcft.alpha_range(1:4:end); % 每4个取1个
beta_range = params.dcft.beta_range(1:4:end);
gamma_range = params.dcft.gamma_range(1:4:end);

best_params = struct('alpha', 0, 'beta', 0, 'gamma', 0);
best_score = -inf;

% 参数搜索
for alpha = alpha_range
    for beta = beta_range
        for gamma = gamma_range
            % 生成补偿信号
            compensation = exp(-1j * 2*pi * (alpha * tm.^2 + beta * tm.^3 + gamma * tm.^4));

            % 应用补偿
            compensated = signal .* compensation;

            % 计算聚焦度
            spectrum = fft(compensated);
            score = calculate_focus_score(spectrum);

            if score > best_score
                best_score = score;
                best_params.alpha = alpha;
                best_params.beta = beta;
                best_params.gamma = gamma;
            end
        end
    end
end

end

function best_params = dcft_fast_search_with_constraints(signal, tm, ...
    alpha_mean, beta_mean, gamma_mean, alpha_std, beta_std, gamma_std, ...
    admm_constraints, params)
%快速DCFT搜索（使用统计约束）

% 基于统计信息缩小搜索范围
alpha_tolerance = max(alpha_std * 3, 32); % 至少保持32的范围
beta_tolerance = max(beta_std * 3, 400);
gamma_tolerance = max(gamma_std * 3, 200);

% 构建约束搜索范围
alpha_center = alpha_mean;
beta_center = beta_mean;
gamma_center = gamma_mean;

% 如果有ADMM约束，进一步调整中心点
if ~isempty(admm_constraints) && isfield(admm_constraints, 'alpha')
    alpha_center = (alpha_center + admm_constraints.alpha.value) / 2;
    beta_center = (beta_center + admm_constraints.beta.value) / 2;
    gamma_center = (gamma_center + admm_constraints.gamma.value) / 2;
end

% 生成约束搜索范围
alpha_range = alpha_center + (-alpha_tolerance:16:alpha_tolerance);
beta_range = beta_center + (-beta_tolerance:200:beta_tolerance);
gamma_range = gamma_center + (-gamma_tolerance:100:gamma_tolerance);

% 确保在原始范围内
alpha_range = alpha_range(alpha_range >= min(params.dcft.alpha_range) & ...
                         alpha_range <= max(params.dcft.alpha_range));
beta_range = beta_range(beta_range >= min(params.dcft.beta_range) & ...
                       beta_range <= max(params.dcft.beta_range));
gamma_range = gamma_range(gamma_range >= min(params.dcft.gamma_range) & ...
                         gamma_range <= max(params.dcft.gamma_range));

best_params = struct('alpha', alpha_center, 'beta', beta_center, 'gamma', gamma_center);
best_score = -inf;

% 快速搜索
for alpha = alpha_range
    for beta = beta_range
        for gamma = gamma_range
            % 生成补偿信号
            compensation = exp(-1j * 2*pi * (alpha * tm.^2 + beta * tm.^3 + gamma * tm.^4));

            % 应用补偿
            compensated = signal .* compensation;

            % 计算聚焦度
            spectrum = fft(compensated);
            score = calculate_focus_score(spectrum);

            % 如果有ADMM约束，添加正则化项
            if ~isempty(admm_constraints)
                regularization = calculate_admm_regularization(alpha, beta, gamma, admm_constraints);
                score = score - params.admm.lambda * regularization;
            end

            if score > best_score
                best_score = score;
                best_params.alpha = alpha;
                best_params.beta = beta;
                best_params.gamma = gamma;
            end
        end
    end
end

end

function best_params = dcft_parameter_search_with_admm(signal, tm, admm_constraints, params)
%带ADMM约束的DCFT参数搜索（保留原函数以兼容性）

% 使用快速搜索替代
best_params = dcft_coarse_search(signal, tm, params);

end

function constrained_range = constrain_search_range(original_range, constraint)
%根据ADMM约束缩小搜索范围

if isempty(constraint)
    constrained_range = original_range;
    return;
end

% 在约束值附近搜索
center = constraint.value;
tolerance = constraint.tolerance;

constrained_range = original_range(abs(original_range - center) <= tolerance);
if isempty(constrained_range)
    constrained_range = original_range; % 如果约束过严，回退到原始范围
end

end

function score = calculate_focus_score(spectrum)
%计算聚焦度分数

magnitude = abs(spectrum);
score = max(magnitude) / mean(magnitude); % 峰值与均值比

end

function regularization = calculate_admm_regularization(alpha, beta, gamma, constraints)
%计算ADMM正则化项

regularization = 0;
if isfield(constraints, 'alpha')
    regularization = regularization + (alpha - constraints.alpha.value)^2;
end
if isfield(constraints, 'beta')
    regularization = regularization + (beta - constraints.beta.value)^2;
end
if isfield(constraints, 'gamma')
    regularization = regularization + (gamma - constraints.gamma.value)^2;
end

end

function [sparse_result, admm_weights] = admm_constrained_reconstruction(signal, phase_estimates, motion_params, params)
%ADMM-约束的稀疏重建（优化版本）

[num_range_bins, num_azimuth] = size(signal);

fprintf('    ADMM稀疏重建进度: ');

% 应用相位补偿
compensated_signal = apply_phase_compensation(signal, phase_estimates);
fprintf('相位补偿完成 -> ');

% ADMM稀疏重建
[sparse_result, admm_weights] = perform_admm_reconstruction(compensated_signal, params);
fprintf('稀疏重建完成\n');

end

function compensated_signal = apply_phase_compensation(signal, phase_estimates)
%应用相位补偿

[num_range_bins, num_azimuth] = size(signal);
compensated_signal = zeros(size(signal));

for r_idx = 1:num_range_bins
    compensation = exp(-1j * phase_estimates(r_idx, :));
    compensated_signal(r_idx, :) = signal(r_idx, :) .* compensation;
end

end

function [sparse_result, weights] = perform_admm_reconstruction(signal, params)
%执行ADMM稀疏重建

% 简化的ADMM实现
rho = params.admm.rho;
lambda = params.admm.lambda;
max_iter = params.admm.max_iter;
tol = params.admm.tol;

[num_range_bins, num_azimuth] = size(signal);

% 初始化ADMM变量
x = signal; % 主变量
z = signal; % 辅助变量
u = zeros(size(signal)); % 拉格朗日乘子

weights = struct();

% ADMM迭代
for iter = 1:max_iter
    x_prev = x;

    % x-更新（数据保真度）
    x = (signal + rho * (z - u)) / (1 + rho);

    % z-更新（稀疏性）
    v = x + u;
    z = soft_threshold(v, lambda/rho);

    % u-更新（拉格朗日乘子）
    u = u + x - z;

    % 检查收敛
    primal_residual = norm(x - z, 'fro');
    dual_residual = rho * norm(z - soft_threshold(v, lambda/rho), 'fro');

    if primal_residual < tol && dual_residual < tol
        break;
    end
end

sparse_result = x;
weights.sparsity = nnz(abs(sparse_result) > 0.01*max(abs(sparse_result(:)))) / numel(sparse_result);

end

function result = soft_threshold(x, threshold)
%软阈值函数

result = sign(x) .* max(abs(x) - threshold, 0);

end

function [fusion_result, converged] = update_fusion_state(fusion_result, vmd_modes, phase_estimates, sparse_result, params)
%更新融合状态并检查收敛

% 计算当前迭代的融合结果
current_result = combine_fusion_components(vmd_modes, phase_estimates, sparse_result, params);

% 检查收敛
if fusion_result.iteration_count > 0
    change = norm(current_result - fusion_result.current_signal, 'fro') / norm(fusion_result.current_signal, 'fro');
    converged = change < params.fusion.convergence_tol;
    fusion_result.convergence_history(end+1) = change;
else
    converged = false;
    fusion_result.convergence_history = [];
end

% 更新状态
fusion_result.current_signal = current_result;
fusion_result.iteration_count = fusion_result.iteration_count + 1;

% 更新ADMM约束（基于当前估计）
fusion_result.admm_constraints = update_admm_constraints(phase_estimates, params);

end

function combined_result = combine_fusion_components(vmd_modes, phase_estimates, sparse_result, params)
%组合融合组件

% 获取权重
w_vmd = params.fusion.vmd_weight;
w_dcft = params.fusion.dcft_weight;
w_admm = params.fusion.admm_weight;

% 归一化权重
total_weight = w_vmd + w_dcft + w_admm;
w_vmd = w_vmd / total_weight;
w_dcft = w_dcft / total_weight;
w_admm = w_admm / total_weight;

% 从VMD模态重建信号
vmd_reconstructed = squeeze(sum(vmd_modes, 1));

% 从相位估计重建信号（应用逆补偿）
[num_range_bins, num_azimuth] = size(phase_estimates);
dcft_reconstructed = zeros(num_range_bins, num_azimuth);
for r_idx = 1:num_range_bins
    compensation = exp(1j * phase_estimates(r_idx, :));
    dcft_reconstructed(r_idx, :) = sparse_result(r_idx, :) .* compensation;
end

% 加权组合
combined_result = w_vmd * vmd_reconstructed + w_dcft * dcft_reconstructed + w_admm * sparse_result;

end

function admm_constraints = update_admm_constraints(phase_estimates, params)
%更新ADMM约束

% 从相位估计中提取运动参数的统计信息
[num_range_bins, num_azimuth] = size(phase_estimates);
tm = (0:num_azimuth-1);

% 对每个距离单元拟合多项式
alpha_estimates = zeros(num_range_bins, 1);
beta_estimates = zeros(num_range_bins, 1);
gamma_estimates = zeros(num_range_bins, 1);

for r_idx = 1:num_range_bins
    phase = phase_estimates(r_idx, :);

    % 多项式拟合
    p = polyfit(tm, phase, 3);
    if length(p) >= 3
        gamma_estimates(r_idx) = p(1);
        beta_estimates(r_idx) = p(2);
        alpha_estimates(r_idx) = p(3);
    end
end

% 计算约束值和容差
admm_constraints = struct();
admm_constraints.alpha.value = median(alpha_estimates);
admm_constraints.alpha.tolerance = std(alpha_estimates) * 2;
admm_constraints.beta.value = median(beta_estimates);
admm_constraints.beta.tolerance = std(beta_estimates) * 2;
admm_constraints.gamma.value = median(gamma_estimates);
admm_constraints.gamma.tolerance = std(gamma_estimates) * 2;

end

function ISAR_image = generate_final_image(fusion_result, params)
%生成最终ISAR图像

% 对融合结果进行方位向FFT
final_signal = fusion_result.current_signal;
ISAR_image = fftshift(fft(final_signal, [], 2), 2);

% 转换为dB
ISAR_image = 20 * log10(abs(ISAR_image) / max(abs(ISAR_image(:))));

end

function enhanced_image = enhance_image(ISAR_image, params)
%图像增强

enhanced_image = ISAR_image;

% 应用动态范围限制
if isfield(params, 'processing') && isfield(params.processing, 'dynamic_range_db')
    dynamic_range = params.processing.dynamic_range_db;
    enhanced_image(enhanced_image < -dynamic_range) = -dynamic_range;
end

% 可以添加其他增强技术，如：
% - 自适应直方图均衡化
% - 边缘增强
% - 噪声抑制

end

function processing_info = generate_processing_info(fusion_result, processing_time, params)
%生成处理信息

processing_info = struct();
processing_info.processing_time = processing_time;
processing_info.iterations = fusion_result.iteration_count;
processing_info.convergence_history = fusion_result.convergence_history;
processing_info.final_convergence = fusion_result.convergence_history(end);
processing_info.params_used = params;

% 计算图像质量指标
final_image = abs(fusion_result.current_signal);
processing_info.image_contrast = std(final_image(:)) / mean(final_image(:));
processing_info.image_entropy = calculate_image_entropy(final_image);
processing_info.sparsity_ratio = nnz(abs(final_image) > 0.01*max(abs(final_image(:)))) / numel(final_image);

fprintf('\n==== 融合算法处理信息 ====\n');
fprintf('处理时间: %.3f 秒\n', processing_time);
fprintf('迭代次数: %d\n', fusion_result.iteration_count);
fprintf('最终收敛误差: %.6f\n', processing_info.final_convergence);
fprintf('图像对比度: %.4f\n', processing_info.image_contrast);
fprintf('图像熵: %.4f\n', processing_info.image_entropy);
fprintf('稀疏度: %.4f\n', processing_info.sparsity_ratio);

end

function entropy = calculate_image_entropy(image)
%计算图像熵

% 归一化图像
normalized = image / sum(image(:));
normalized(normalized == 0) = eps; % 避免log(0)

% 计算熵
entropy = -sum(normalized(:) .* log2(normalized(:)));

end
