%-------------------------------------------------------------------------%
%--------   DCFT (Discrete Cubic Fourier Transform) ISAR成像处理器  -------%
%--------   可直接处理距离压缩后的回波数据                       -------%
%-------------------------------------------------------------------------%
function [ISAR_image, processing_info] = DCFT_ISAR_Processor(echo_data, params)
% DCFT_ISAR_Processor - 基于DCFT的ISAR成像处理器
%
% 输入:
%   echo_data - 距离压缩后的回波数据，矩阵大小为 [距离单元数 x 方位采样点数]
%   params - 处理参数结构体，包含以下字段(均为可选):
%     .radar - 雷达参数
%       .fc - 载频(Hz)，默认5.2e9
%       .B - 带宽(Hz)，默认80e6
%       .PRF - 脉冲重复频率(Hz)，默认1400
%     .processing - 处理参数
%       .range_bins - 需要处理的距离单元范围，默认全部处理
%       .motion_comp - 是否进行运动补偿，默认false
%       .doppler_shift - 多普勒中心频率(Hz)，默认0
%       .apply_window - 是否应用窗函数，默认true
%       .dynamic_range_db - 显示动态范围(dB)，默认30
%     .dcft - DCFT参数
%       .alpha_step - 啁啾率搜索步长，默认8
%       .alpha_min - 最小啁啾率，默认-16
%       .alpha_max - 最大啁啾率，默认320
%       .beta_step - 啁啾率导数搜索步长，默认100
%       .beta_min - 最小啁啾率导数，默认-500
%       .beta_max - 最大啁啾率导数，默认2400
%       .thresholding - 是否进行阈值处理，默认true
%       .threshold_ratio - 阈值比例，默认0.2
%
% 输出:
%   ISAR_image - DCFT ISAR成像结果，单位为dB
%   processing_info - 处理信息结构体

% 1. 检查输入参数并设置默认值
if nargin < 2
    params = struct();
end

% 获取数据尺寸
[num_range_bins, num_azimuth] = size(echo_data);
fprintf('数据尺寸: %d x %d (距离单元 x 方位单元)\n', num_range_bins, num_azimuth);

% 设置默认雷达参数
if ~isfield(params, 'radar')
    params.radar = struct();
end
if ~isfield(params.radar, 'fc')
    params.radar.fc = 5.2e9; % Hz
end
if ~isfield(params.radar, 'B')
    params.radar.B = 80e6; % Hz
end
if ~isfield(params.radar, 'PRF')
    params.radar.PRF = 1400; % Hz
end

% 设置默认处理参数
if ~isfield(params, 'processing')
    params.processing = struct();
end
if ~isfield(params.processing, 'range_bins')
    params.processing.range_bins = 1:num_range_bins;
elseif ischar(params.processing.range_bins) && strcmp(params.processing.range_bins, 'auto')
    % 自动检测有意义的距离单元
    energy = sum(abs(echo_data).^2, 2);
    threshold = mean(energy) + 3*std(energy);
    valid_bins = find(energy > threshold);
    params.processing.range_bins = max(1, min(valid_bins)-5):min(num_range_bins, max(valid_bins)+5);
    fprintf('自动选择距离单元范围: %d - %d\n', params.processing.range_bins(1), params.processing.range_bins(end));
end

if ~isfield(params.processing, 'motion_comp')
    params.processing.motion_comp = false;
end
if ~isfield(params.processing, 'doppler_shift')
    params.processing.doppler_shift = 0;
end
if ~isfield(params.processing, 'apply_window')
    params.processing.apply_window = true;
end
if ~isfield(params.processing, 'dynamic_range_db')
    params.processing.dynamic_range_db = 30;
end

% 设置默认DCFT参数
if ~isfield(params, 'dcft')
    params.dcft = struct();
end
if ~isfield(params.dcft, 'alpha_step')
    params.dcft.alpha_step = 8;
end
if ~isfield(params.dcft, 'alpha_min')
    params.dcft.alpha_min = -2 * params.dcft.alpha_step;
end
if ~isfield(params.dcft, 'alpha_max')
    params.dcft.alpha_max = 40 * params.dcft.alpha_step;
end
if ~isfield(params.dcft, 'beta_step')
    params.dcft.beta_step = 100;
end
if ~isfield(params.dcft, 'beta_min')
    params.dcft.beta_min = -5 * params.dcft.beta_step;
end
if ~isfield(params.dcft, 'beta_max')
    params.dcft.beta_max = 24 * params.dcft.beta_step;
end
if ~isfield(params.dcft, 'thresholding')
    params.dcft.thresholding = true;
end
if ~isfield(params.dcft, 'threshold_ratio')
    params.dcft.threshold_ratio = 0.2;
end

% 2. 数据预处理
processed_data = echo_data;

% 2.1 窗函数处理
if params.processing.apply_window
    % 应用汉明窗进行旁瓣抑制
    window = hamming(num_azimuth)';
    window_matrix = repmat(window, num_range_bins, 1);
    processed_data = processed_data .* window_matrix;
end

% 2.2 运动补偿 (如果需要)
if params.processing.motion_comp
    fprintf('应用运动补偿...\n');
    % 生成时间向量
    tm = (0:num_azimuth-1) / params.radar.PRF;
    
    % 多普勒频移补偿
    if params.processing.doppler_shift ~= 0
        phase_comp = exp(1j * 2*pi * params.processing.doppler_shift * tm);
        processed_data = processed_data .* repmat(phase_comp, num_range_bins, 1);
    end
    
    % 这里可以添加更复杂的运动补偿，如二阶和三阶相位补偿
    % 实际应用中应该基于数据估计这些参数
end

% 3. 传统FFT成像结果 (用于比较)
tic;
traditional_image = fftshift(fft(processed_data, [], 2), 2);
traditional_time = toc;
fprintf('传统FFT成像完成，耗时: %.3f 秒\n', traditional_time);

% 显示传统FFT成像结果
traditional_db = 20*log10(abs(traditional_image)./max(abs(traditional_image(:))));
figure('Name', '传统FFT成像结果');
imagesc(traditional_db);
caxis([-params.processing.dynamic_range_db, 0]);
colormap(jet); colorbar;
title('传统FFT成像结果 (dB)');
xlabel('方位单元');
ylabel('距离单元');
axis xy;

% 4. DCFT处理
tic;
% 选择目标的距离单元范围
range_bins = params.processing.range_bins;
fprintf('处理距离单元范围: %d - %d\n', range_bins(1), range_bins(end));

% 准备DCFT参数
alpha_values = params.dcft.alpha_min:params.dcft.alpha_step:params.dcft.alpha_max;
beta_values = params.dcft.beta_min:params.dcft.beta_step:params.dcft.beta_max;
num_alpha = length(alpha_values);
num_beta = length(beta_values);
fprintf('DCFT参数空间: 啁啾率 [%d-%d], 步长 %d, 啁啾率导数 [%d-%d], 步长 %d\n', ...
        params.dcft.alpha_min, params.dcft.alpha_max, params.dcft.alpha_step, ...
        params.dcft.beta_min, params.dcft.beta_max, params.dcft.beta_step);

% 生成时间向量
tm = (0:num_azimuth-1) / params.radar.PRF;

% 初始化DCFT结果矩阵
DCFT_result = zeros(num_range_bins, num_azimuth);

% 对选定距离单元进行DCFT处理
for idx = 1:length(range_bins)
    r_idx = range_bins(idx);
    
    % 显示进度
    if mod(idx, 5) == 0 || idx == 1 || idx == length(range_bins)
        fprintf('处理距离单元 %d/%d (%.1f%%)\n', idx, length(range_bins), 100*idx/length(range_bins));
    end
    
    % 提取当前距离单元的信号
    signal = processed_data(r_idx, :);
    
    % 初始化频率分布
    freq_distribution = zeros(1, num_azimuth);
    
    % 初始化最大值跟踪
    max_response = 0;
    
    % 第一遍: 查找最大响应
    for a_idx = 1:num_alpha
        alpha = alpha_values(a_idx);
        
        for b_idx = 1:num_beta
            beta = beta_values(b_idx);
            
            % 生成补偿信号
            compensation = exp(-1j * 2*pi * ((1/2)*alpha*tm.*tm + (1/6)*beta*tm.*tm.*tm));
            
            % 应用去啁啾
            dechirped = signal .* compensation;
            
            % FFT处理
            spectrum = fft(dechirped);
            
            % 更新最大响应
            current_max = max(abs(spectrum));
            if current_max > max_response
                max_response = current_max;
            end
        end
    end
    
    % 第二遍: 累积所有有效响应
    for a_idx = 1:num_alpha
        alpha = alpha_values(a_idx);
        
        for b_idx = 1:num_beta
            beta = beta_values(b_idx);
            
            % 生成补偿信号
            compensation = exp(-1j * 2*pi * ((1/2)*alpha*tm.*tm + (1/6)*beta*tm.*tm.*tm));
            
            % 应用去啁啾
            dechirped = signal .* compensation;
            
            % FFT处理
            spectrum = fft(dechirped);
            
            % 累积频率分布
            freq_distribution = freq_distribution + spectrum;
        end
    end
    
    % 存储结果
    DCFT_result(r_idx, :) = abs(freq_distribution);
end

% 5. 后处理
% 阈值处理
if params.dcft.thresholding
    for r_idx = range_bins
        bin_max = max(DCFT_result(r_idx, :));
        threshold = bin_max * params.dcft.threshold_ratio;
        DCFT_result(r_idx, DCFT_result(r_idx, :) < threshold) = 0;
    end
end

DCFT_time = toc;
fprintf('DCFT处理完成，耗时: %.3f 秒\n', DCFT_time);

% 6. 生成最终图像
ISAR_image = 20*log10(abs(DCFT_result)./max(abs(DCFT_result(:))));

% 显示DCFT成像结果
figure('Name', 'DCFT成像结果');
imagesc(ISAR_image);
caxis([-params.processing.dynamic_range_db, 0]);
colormap(jet); colorbar;
title('DCFT成像结果 (dB)');
xlabel('方位单元');
ylabel('距离单元');
axis xy;

% 7. 图像质量评估
% 计算对比度
contrast_traditional = calculate_contrast(traditional_image);
contrast_dcft = calculate_contrast(DCFT_result);

% 计算熵
entropy_traditional = calculate_entropy(abs(traditional_image));
entropy_dcft = calculate_entropy(abs(DCFT_result));

fprintf('\n==== 图像质量评估 ====\n');
fprintf('传统FFT成像:\n');
fprintf('  - 对比度: %.4f\n', contrast_traditional);
fprintf('  - 熵: %.4f\n', entropy_traditional);
fprintf('  - 处理时间: %.3f 秒\n', traditional_time);

fprintf('DCFT成像:\n');
fprintf('  - 对比度: %.4f (提升: %.2f%%)\n', contrast_dcft, 100*(contrast_dcft-contrast_traditional)/contrast_traditional);
fprintf('  - 熵: %.4f (降低: %.2f%%)\n', entropy_dcft, 100*(entropy_traditional-entropy_dcft)/entropy_traditional);
fprintf('  - 处理时间: %.3f 秒\n', DCFT_time);

% 8. 返回处理信息
processing_info = struct();
processing_info.contrast = struct('traditional', contrast_traditional, 'dcft', contrast_dcft);
processing_info.entropy = struct('traditional', entropy_traditional, 'dcft', entropy_dcft);
processing_info.time = struct('traditional', traditional_time, 'dcft', DCFT_time);
processing_info.params = params;

end

% ===== 辅助函数 =====

function contrast = calculate_contrast(image)
    % 计算图像对比度
    magnitude = abs(image);
    contrast = std(magnitude(:)) / mean(magnitude(:));
end

function entropy = calculate_entropy(image)
    % 计算图像熵
    normalized = image / sum(image(:));
    entropy = -sum(normalized(:) .* log2(normalized(:) + eps));
end 

EntropyImage(traditional_image+eps)
contrast(traditional_image)
EntropyImage(DCFT_result)
contrast(DCFT_result)
