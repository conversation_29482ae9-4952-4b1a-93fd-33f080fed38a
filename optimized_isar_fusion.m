% 高效优化版VMD-ADMM-DCFT融合ISAR成像算法
% 专注于计算效率优化，保持成像质量

function optimized_isar_fusion()
    clc; clear; close all;

    fprintf('=== 高效优化版VMD-ADMM-DCFT融合ISAR成像算法 ===\n');
    fprintf('优化特点: 向量化计算、并行处理、内存优化、算法简化\n\n');

    % 1. 生成仿真数据
    [shipx2, radar_params] = generate_ship_data();

    % 2. 优化参数配置
    params = configure_optimized_parameters();

    % 3. 传统FFT成像（基准）
    fprintf('执行传统FFT成像...\n');
    tic;
    traditional_image = fftshift(fft(shipx2, [], 2), 2);
    traditional_time = toc;

    % 4. 优化版VMD-ADMM-DCFT融合成像
    fprintf('执行优化版VMD-ADMM-DCFT融合成像...\n');
    tic;
    [optimized_image, processing_info] = optimized_vmd_admm_dcft_fusion(shipx2, params);
    optimized_time = toc;

    % 5. 结果分析与可视化
    analyze_and_visualize_results(traditional_image, optimized_image, ...
                                  traditional_time, optimized_time, processing_info);
end

function params = configure_optimized_parameters()
    % 优化的算法参数配置 - 平衡性能和质量
    params = struct();

    % VMD参数 - 简化但有效
    params.vmd.K = 3;                    % 减少模态数
    params.vmd.alpha = 2000;             % 适中的惩罚因子
    params.vmd.tau = 0.25;
    params.vmd.tol = 1e-6;               % 放宽收敛精度
    params.vmd.max_iter = 200;           % 减少迭代次数
    params.vmd.adaptive_K = false;       % 关闭自适应以提高速度
    params.vmd.use_parallel = true;      % 启用并行处理

    % DCFT参数 - 大幅简化搜索空间
    params.dcft.alpha_range = [-100, 100];  % 缩小搜索范围
    params.dcft.alpha_step = 20;             % 增大步长
    params.dcft.beta_range = [-500, 500];
    params.dcft.beta_step = 100;
    params.dcft.use_fourth_order = false;   % 关闭四阶项
    params.dcft.use_fine_search = false;    % 关闭精细搜索
    params.dcft.use_vectorized = true;      % 启用向量化
    params.dcft.phase_unwrap = false;       % 简化相位处理

    % ADMM参数 - 快速收敛
    params.admm.rho = 1.0;
    params.admm.max_iter = 50;              % 大幅减少迭代
    params.admm.tol = 1e-4;                 % 放宽收敛条件
    params.admm.dynamic_rho = false;        % 关闭动态调整
    params.admm.use_frequency_weights = false;  % 简化权重计算
    params.admm.use_continuity_constraint = false;  % 关闭连续性约束
    params.admm.use_sparsity_adaptive = false;      % 关闭自适应稀疏性

    % 融合参数 - 简化策略
    params.fusion.vmd_weight = 0.4;
    params.fusion.dcft_weight = 0.4;
    params.fusion.admm_weight = 0.2;
    params.fusion.use_iterative_refinement = false;  % 关闭迭代优化

    % 性能优化参数
    params.performance.use_parallel = true;
    params.performance.batch_size = 10;              % 批处理大小
    params.performance.show_progress = true;
    params.performance.save_intermediate = false;    % 关闭中间结果保存
    params.performance.use_gpu = false;              % GPU加速（可选）
    params.performance.memory_efficient = true;     % 内存优化模式
end

function [optimized_image, processing_info] = optimized_vmd_admm_dcft_fusion(radar_data, params)
    % 优化版VMD-ADMM-DCFT融合算法主函数

    [num_range_bins, num_azimuth] = size(radar_data);
    fprintf('处理数据尺寸: %d x %d\n', num_range_bins, num_azimuth);

    % 数据预处理 - 简化版
    radar_data = simple_preprocess(radar_data);

    % 初始化处理信息
    processing_info = struct();
    processing_info.processing_time = struct();

    % 创建时间轴
    tm = (0:num_azimuth-1) / num_azimuth;

    % 初始化补偿后的信号
    s_compensated = zeros(size(radar_data), 'like', radar_data);

    % 批处理优化
    batch_size = params.performance.batch_size;
    num_batches = ceil(num_range_bins / batch_size);

    fprintf('开始批处理优化...\n');
    for batch_idx = 1:num_batches
        if params.performance.show_progress
            fprintf('处理批次: %d/%d (%.1f%%)\n', batch_idx, num_batches, 100*batch_idx/num_batches);
        end

        % 确定批次范围
        start_idx = (batch_idx - 1) * batch_size + 1;
        end_idx = min(batch_idx * batch_size, num_range_bins);
        batch_indices = start_idx:end_idx;

        % 批处理当前范围的距离单元
        batch_data = radar_data(batch_indices, :);
        batch_compensated = process_range_batch(batch_data, tm, params);

        % 存储结果
        s_compensated(batch_indices, :) = batch_compensated;
    end

    % 后处理增强 - 简化版
    s_compensated = simple_post_processing(s_compensated);

    % 方位向FFT成像
    optimized_image = fftshift(fft(s_compensated, [], 2), 2);

    % 更新处理信息
    processing_info.compensated_data = s_compensated;
    processing_info.final_image = optimized_image;

    fprintf('优化版VMD-ADMM-DCFT处理完成\n');
end

function radar_data = simple_preprocess(radar_data)
    % 简化的数据预处理

    % 1. 快速归一化
    max_val = max(abs(radar_data(:)));
    if max_val > 0
        radar_data = radar_data / max_val;
    end

    % 2. 去除直流分量（向量化）
    radar_data = radar_data - mean(radar_data, 2);
end

function batch_compensated = process_range_batch(batch_data, tm, params)
    % 批处理距离单元

    [batch_size, num_azimuth] = size(batch_data);
    batch_compensated = zeros(size(batch_data), 'like', batch_data);

    % 对批次中的每个距离单元进行处理
    for i = 1:batch_size
        signal = batch_data(i, :);

        % 跳过低能量信号
        if sum(abs(signal).^2) < 1e-10
            batch_compensated(i, :) = signal;
            continue;
        end

        % 1. 快速VMD分解
        vmd_modes = fast_vmd_decomposition(signal, params.vmd);

        % 2. 简化DCFT相位估计
        phase_estimates = fast_dcft_estimation(vmd_modes, tm, params.dcft);

        % 3. 快速ADMM重建
        reconstructed_signal = fast_admm_reconstruction(signal, vmd_modes, phase_estimates, params.admm);

        % 4. 简化融合
        compensated_signal = simple_fusion(signal, vmd_modes, phase_estimates, reconstructed_signal, params.fusion);

        % 存储结果
        batch_compensated(i, :) = compensated_signal;
    end
end

function vmd_modes = fast_vmd_decomposition(signal, vmd_params)
    % 快速VMD分解 - 简化版本

    N = length(signal);
    K = vmd_params.K;
    alpha = vmd_params.alpha;
    tau = vmd_params.tau;
    tol = vmd_params.tol;
    max_iter = vmd_params.max_iter;

    % 频域信号
    signal_fft = fft(signal);
    freqs = (0:N-1)/N;

    % 初始化
    u_k = zeros(K, N, 'like', 1j);
    omega_k = (1:K) / (2*K);  % 简化初始化
    lambda = zeros(1, N, 'like', 1j);

    % 简化的VMD迭代
    for iter = 1:max_iter
        u_k_prev = u_k;

        % 向量化更新所有模态
        sum_uk = sum(u_k, 1);

        for k = 1:K
            % 更新u_k
            sum_u_i = sum_uk - u_k(k, :);
            num = signal_fft - fft(sum_u_i) + fft(lambda)/2;
            den = 1 + 2*alpha*(freqs - omega_k(k)).^2;

            u_k_fft = num ./ den;
            u_k(k, :) = ifft(u_k_fft);

            % 更新中心频率
            power_spectrum = abs(u_k_fft).^2;
            if sum(power_spectrum) > 0
                omega_k(k) = sum(freqs .* power_spectrum) / sum(power_spectrum);
            end
        end

        % 更新拉格朗日乘子
        lambda = lambda + tau * (signal - sum(u_k, 1));

        % 简化收敛检查
        if norm(u_k - u_k_prev, 'fro') < tol * norm(u_k_prev, 'fro')
            break;
        end
    end

    vmd_modes = u_k;
end

function phase_estimates = fast_dcft_estimation(vmd_modes, tm, dcft_params)
    % 快速DCFT相位估计 - 大幅简化搜索空间

    [K, N] = size(vmd_modes);
    phase_estimates = zeros(K, N);

    % 简化的参数搜索范围
    alpha_range = dcft_params.alpha_range(1):dcft_params.alpha_step:dcft_params.alpha_range(2);
    beta_range = dcft_params.beta_range(1):dcft_params.beta_step:dcft_params.beta_range(2);

    % 向量化搜索（如果启用）
    if dcft_params.use_vectorized
        phase_estimates = vectorized_dcft_search(vmd_modes, tm, alpha_range, beta_range);
        return;
    end

    % 传统搜索但简化
    for k = 1:K
        mode_signal = vmd_modes(k, :);

        % 跳过低能量模态
        if sum(abs(mode_signal).^2) < 1e-12
            continue;
        end

        % 快速初始估计
        analytic_signal = hilbert(mode_signal);
        inst_freq = diff(unwrap(angle(analytic_signal))) / (2*pi);

        % 简化的多项式拟合
        if length(inst_freq) > 2
            poly_coeffs = polyfit((1:length(inst_freq))/length(inst_freq), inst_freq, 1);
            fd_init = mean(inst_freq);
            alpha_init = poly_coeffs(1) * 2;
        else
            fd_init = 0;
            alpha_init = 0;
        end

        % 简化搜索
        best_response = -inf;
        best_params = [fd_init, alpha_init, 0];

        % 缩小搜索范围
        fd_search = fd_init + (-0.05:0.02:0.05);
        alpha_search = alpha_init + alpha_range(1:2:end);  % 跳步搜索
        beta_search = beta_range(1:3:end);  % 跳步搜索

        % 三重循环（去掉gamma）
        for fd = fd_search
            for alpha = alpha_search
                for beta = beta_search
                    % 构建去啁啾项（三阶）
                    dechirp_phase = -1j * 2*pi * (fd*tm + (1/2)*alpha*tm.^2 + (1/6)*beta*tm.^3);
                    dechirped = mode_signal .* exp(dechirp_phase);

                    % FFT处理并计算响应
                    response = max(abs(fft(dechirped)));

                    if response > best_response
                        best_response = response;
                        best_params = [fd, alpha, beta];
                    end
                end
            end
        end

        % 构建相位模型
        fd = best_params(1);
        alpha = best_params(2);
        beta = best_params(3);

        phase_estimates(k, :) = 2*pi * (fd*tm + (1/2)*alpha*tm.^2 + (1/6)*beta*tm.^3);
    end
end

function phase_estimates = vectorized_dcft_search(vmd_modes, tm, alpha_range, beta_range)
    % 向量化DCFT搜索 - 显著提高效率

    [K, N] = size(vmd_modes);
    phase_estimates = zeros(K, N);

    % 预计算时间项
    tm_matrix = repmat(tm, length(alpha_range), 1);
    alpha_matrix = repmat(alpha_range', 1, N);

    for k = 1:K
        mode_signal = vmd_modes(k, :);

        if sum(abs(mode_signal).^2) < 1e-12
            continue;
        end

        best_response = -inf;
        best_alpha = 0;
        best_beta = 0;

        % 向量化alpha搜索
        alpha_phases = alpha_matrix .* (tm_matrix.^2) / 2;

        for beta = beta_range
            % 向量化计算所有alpha的响应
            beta_phase = beta * (tm.^3) / 6;
            total_phases = alpha_phases + repmat(beta_phase, length(alpha_range), 1);

            % 批量计算响应
            responses = zeros(length(alpha_range), 1);
            for i = 1:length(alpha_range)
                dechirped = mode_signal .* exp(-1j * 2*pi * total_phases(i, :));
                responses(i) = max(abs(fft(dechirped)));
            end

            % 找到最佳响应
            [max_response, max_idx] = max(responses);
            if max_response > best_response
                best_response = max_response;
                best_alpha = alpha_range(max_idx);
                best_beta = beta;
            end
        end

        % 构建最终相位模型
        phase_estimates(k, :) = 2*pi * ((1/2)*best_alpha*tm.^2 + (1/6)*best_beta*tm.^3);
    end
end

function reconstructed_signal = fast_admm_reconstruction(original_signal, vmd_modes, phase_estimates, admm_params)
    % 快速ADMM重建 - 简化版本

    [K, N] = size(vmd_modes);

    % 构建补偿后的模态信号
    compensated_modes = zeros(K, N, 'like', 1j);
    for k = 1:K
        compensated_modes(k, :) = vmd_modes(k, :) .* exp(-1j * phase_estimates(k, :));
    end

    % 简化的ADMM变量初始化
    X = fft(original_signal);
    Z = X;
    Lambda = zeros(size(X), 'like', 1j);

    % ADMM参数
    rho = admm_params.rho;
    max_iter = admm_params.max_iter;
    tol = admm_params.tol;

    % 构建目标信号
    Y_target = sum(fft(compensated_modes, [], 2), 1);

    % 简化的ADMM迭代
    for iter = 1:max_iter
        X_prev = X;

        % 更新X
        X = (Y_target + rho * Z - Lambda) ./ (1 + rho);

        % 更新Z（简化的软阈值）
        Z_update = X + Lambda/rho;
        threshold = 0.1/rho;  % 固定阈值
        Z = sign(Z_update) .* max(abs(Z_update) - threshold, 0);

        % 更新Lambda
        Lambda = Lambda + rho * (X - Z);

        % 简化收敛检查
        if norm(X - X_prev) < tol * norm(X_prev)
            break;
        end
    end

    % 重构时域信号
    reconstructed_signal = ifft(X);
end

function compensated_signal = simple_fusion(original_signal, vmd_modes, phase_estimates, reconstructed_signal, fusion_params)
    % 简化融合算法

    [K, N] = size(vmd_modes);

    % 1. VMD补偿 - 只使用主导模态
    mode_energies = sum(abs(vmd_modes).^2, 2);
    [~, dominant_idx] = max(mode_energies);

    vmd_compensated = vmd_modes(dominant_idx, :) .* exp(-1j * phase_estimates(dominant_idx, :));

    % 2. DCFT补偿 - 简化版
    total_phase = sum(phase_estimates .* (mode_energies / sum(mode_energies)), 1);
    dcft_compensated = original_signal .* exp(-1j * total_phase);

    % 3. 简单加权融合
    w1 = fusion_params.vmd_weight;
    w2 = fusion_params.dcft_weight;
    w3 = fusion_params.admm_weight;

    % 归一化权重
    total_weight = w1 + w2 + w3;
    w1 = w1 / total_weight;
    w2 = w2 / total_weight;
    w3 = w3 / total_weight;

    % 融合
    compensated_signal = w1 * vmd_compensated + w2 * dcft_compensated + w3 * reconstructed_signal;
end

function s_enhanced = simple_post_processing(s_compensated)
    % 简化后处理

    [num_range_bins, num_azimuth] = size(s_compensated);
    s_enhanced = s_compensated;

    % 1. 简单对比度增强
    for r_idx = 1:num_range_bins
        signal = s_enhanced(r_idx, :);
        if sum(abs(signal).^2) > 1e-10
            signal_mag = abs(signal);
            signal_phase = angle(signal);

            % 简单的幅度增强
            enhanced_mag = signal_mag .^ 0.9;  % 轻微非线性增强
            s_enhanced(r_idx, :) = enhanced_mag .* exp(1j * signal_phase);
        end
    end

    % 2. 简单旁瓣抑制
    s_enhanced = simple_sidelobe_suppression(s_enhanced);
end

function s_suppressed = simple_sidelobe_suppression(s_compensated)
    % 简化旁瓣抑制

    [num_range_bins, num_azimuth] = size(s_compensated);
    s_suppressed = s_compensated;

    % 向量化处理
    for r_idx = 1:num_range_bins
        signal = s_compensated(r_idx, :);

        if sum(abs(signal).^2) > 1e-10
            % FFT
            signal_fft = fft(signal);
            signal_mag = abs(signal_fft);

            % 找主峰
            [max_val, main_peak_idx] = max(signal_mag);

            % 简单抑制策略
            suppression_factor = ones(1, num_azimuth);
            threshold = 0.3 * max_val;

            % 向量化抑制
            distance_from_peak = abs((1:num_azimuth) - main_peak_idx);
            sidelobe_mask = (distance_from_peak > 5) & (signal_mag > threshold);
            suppression_factor(sidelobe_mask) = 0.6;

            % 应用抑制
            s_suppressed(r_idx, :) = ifft(signal_fft .* suppression_factor);
        end
    end
end

function [shipx2, radar_params] = generate_ship_data()
    % 高效的舰船数据生成

    % 舰船散射点模型（简化版）
    Pos = [-10 -1 0;-9 -1 0;-8 -1 0;-7 -1 0;-6 -1 0;-5 -1 0;-3.75 -1 0;-3 -1 0;-2 -1 0;-1 -1 0;...
           0 -1 0;...
           1 -1 0;2 -1 0;3 -1 0;4 -1 0;5 -1 0;6 -1 0;7 -1 0;8 -1 0;9 -1 0;10 -1 0;...
           -9.5 0.2 0.5;...
           -9.5 1.2 0.5;-9 1 0;-8 1 0;-7 1 0;-6 1 0;-5.2 1.2 0;-4.1 1 0;-3 1 0;-2 1 0;-1 1 0;...
           0 1 0;...
           1 1 0;2 1 0;3 1 0;4 1 0;5 1 0;6 1 0;7 1 0;8 1 0;9 1 0;10 1 0;...
           10.5 -0.75 0;10.5 0.75 0;11 -0.5 0;11 0.5 0;11.5 0 0;...
           9.5 0.5 0.5;9.5 -0.5 0.5;9 0 0.5;8.5 0.5 0.5;8.5 -0.5 0.5;...
           5 0 0.5;5 0 1;5 0 1.5;5 0 2;5 0 2.5;5 0 3;5 0 3.5;5 0 4;...
           5.5 0.5 0.5;5.5 -0.5 0.5;4.5 0.5 0.5;4.5 -0.5 0.5;...
           0.5 0.5 0.9;0.5 -0.5 0.9;-0.5 0.5 0.9;-0.5 -0.5 0.9;0 0 0.5;...
           -5 0 0.2;-5 0.2 0.8;-5 0.2 1.4;-5 0 2;...
           -5.5 -0.5 0.5;-5.5 0.5 0.5;-4.4 0.5 0.5;-4.5 -0.6 0.5;...
           ];

    % 向量化坐标变换
    Pos_scaled = Pos * 5;
    Pos_centered = Pos_scaled - min(Pos_scaled, [], 1);

    x_Pos = Pos_centered(:, 1);
    y_Pos = Pos_centered(:, 2);
    z_Pos = Pos_centered(:, 3);

    % 雷达视线方向
    R = [cos(3*pi/8)*cos(0), cos(3*pi/8)*sin(0), sin(3*pi/8)];
    Num_point = length(x_Pos);

    % 向量化相对位置计算
    x_r = y_Pos*R(3) - z_Pos*R(2);
    y_r = z_Pos*R(1) - x_Pos*R(3);
    z_r = x_Pos*R(2) - y_Pos*R(1);

    % 运动参数
    x_omega = 0.05; y_omega = 0.2; z_omega = 0.05;
    x_lambda = 0.05; y_lambda = 0.1; z_lambda = 0.05;
    x_gamma = 0.05; y_gamma = 0.4; z_gamma = 0.05;

    % 向量化运动参数计算
    f = x_r*x_omega + y_r*y_omega + z_r*z_omega;
    alpha = x_r*x_lambda + y_r*y_lambda + z_r*z_lambda;
    beta = x_r*x_gamma + y_r*y_gamma + z_r*z_gamma;

    % 雷达参数
    B = 80*1e6; c = 3*1e8; PRF = 1400; fc = 5.2*1e9;
    delta_r = c/(2*B);
    r = -50*delta_r:delta_r:50*delta_r;
    tm = 0:(1/PRF):0.501;
    Num_r = length(r); Num_tm = length(tm);

    % 向量化回波生成
    Delta_R0 = x_Pos*R(1) + y_Pos*R(2) + z_Pos*R(3);

    % 预分配内存
    s_r_tm = zeros(Num_r, Num_tm, 'like', 1j);

    % 向量化时间项计算
    tm_matrix = repmat(tm, Num_point, 1);
    f_matrix = repmat(f, 1, Num_tm);
    alpha_matrix = repmat(alpha, 1, Num_tm);
    beta_matrix = repmat(beta, 1, Num_tm);
    Delta_R0_matrix = repmat(Delta_R0, 1, Num_tm);

    % 批量计算Delta_R
    Delta_R = f_matrix .* tm_matrix + (1/2) * alpha_matrix .* (tm_matrix.^2) + ...
              (1/6) * beta_matrix .* (tm_matrix.^3) + Delta_R0_matrix;

    % 批量计算相位
    sita_Delta_R = 4*pi*(fc/c) * Delta_R;

    % 向量化散射强度
    scattering_strength = ones(Num_point, 1);
    scattering_strength(54:61) = 1.3;  % 特殊散射点
    scattering_strength(48) = 1.2;

    % 高效回波计算
    for n_point = 1:Num_point
        % 向量化sinc计算
        r_matrix = repmat(r', 1, Num_tm);
        Delta_R_point = repmat(Delta_R(n_point, :), Num_r, 1);

        sinc_arg = (2*B/c) * (r_matrix - Delta_R_point);
        sinc_values = sinc(sinc_arg);

        phase_values = repmat(sita_Delta_R(n_point, :), Num_r, 1);

        s_r_tm = s_r_tm + scattering_strength(n_point) * sinc_values .* exp(1j * phase_values);
    end

    shipx2 = s_r_tm;

    % 雷达参数
    radar_params = struct('B', B, 'c', c, 'PRF', PRF, 'fc', fc);
end

function analyze_and_visualize_results(traditional_image, optimized_image, traditional_time, optimized_time, processing_info)
    % 快速结果分析与可视化

    fprintf('\n=== 优化版成像结果分析 ===\n');

    % 快速质量指标计算
    contrast_traditional = calculate_contrast(traditional_image);
    contrast_optimized = calculate_contrast(optimized_image);

    entropy_traditional = calculate_entropy(abs(traditional_image));
    entropy_optimized = calculate_entropy(abs(optimized_image));

    % 计算改进百分比
    contrast_improvement = 100 * (contrast_optimized - contrast_traditional) / contrast_traditional;
    entropy_improvement = 100 * (entropy_traditional - entropy_optimized) / entropy_traditional;
    speed_improvement = 100 * (traditional_time - optimized_time) / traditional_time;

    % 打印结果
    fprintf('传统FFT方法:\n');
    fprintf('  对比度: %.4f\n', contrast_traditional);
    fprintf('  熵: %.4f\n', entropy_traditional);
    fprintf('  处理时间: %.2f秒\n', traditional_time);

    fprintf('\n优化版VMD-ADMM-DCFT融合方法:\n');
    fprintf('  对比度: %.4f (改进 %.1f%%)\n', contrast_optimized, contrast_improvement);
    fprintf('  熵: %.4f (改进 %.1f%%)\n', entropy_optimized, entropy_improvement);
    fprintf('  处理时间: %.2f秒 (提速 %.1f%%)\n', optimized_time, speed_improvement);

    % 快速可视化
    figure('Name', '优化版ISAR成像结果对比', 'Position', [100, 100, 1200, 500]);

    % 传统FFT结果
    subplot(1, 2, 1);
    traditional_db = 20*log10(abs(traditional_image) / max(abs(traditional_image(:))));
    imagesc(traditional_db);
    caxis([-40, 0]);
    colormap('jet');
    colorbar;
    title('传统FFT成像结果');
    xlabel('方位单元');
    ylabel('距离单元');
    axis xy;

    % 优化版融合结果
    subplot(1, 2, 2);
    optimized_db = 20*log10(abs(optimized_image) / max(abs(optimized_image(:))));
    imagesc(optimized_db);
    caxis([-40, 0]);
    colormap('jet');
    colorbar;
    title('优化版VMD-ADMM-DCFT融合结果');
    xlabel('方位单元');
    ylabel('距离单元');
    axis xy;

    % 保存图像
    saveas(gcf, 'optimized_ISAR_comparison_results.png');
    fprintf('\n对比结果已保存为: optimized_ISAR_comparison_results.png\n');
end

function contrast_val = calculate_contrast(image)
    % 快速对比度计算
    mag = abs(image);
    contrast_val = std(mag(:)) / (mean(mag(:)) + eps);
end

function entropy_val = calculate_entropy(image)
    % 快速熵计算
    normalized = image / (sum(image(:)) + eps);
    normalized(normalized <= 0) = eps;
    entropy_val = -sum(normalized(:) .* log2(normalized(:)));
end
