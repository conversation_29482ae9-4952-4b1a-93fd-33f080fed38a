% 运行优化版VMD-ADMM-DCFT融合ISAR成像算法
% 专注于计算效率优化

clc; clear; close all;

fprintf('=== 启动优化版VMD-ADMM-DCFT融合ISAR成像算法 ===\n');
fprintf('优化特点:\n');
fprintf('1. 大幅简化搜索空间 (O(N⁴) → O(N³))\n');
fprintf('2. 向量化计算和批处理\n');
fprintf('3. 减少迭代次数和精度要求\n');
fprintf('4. 内存优化和快速收敛\n');
fprintf('5. 保持核心成像功能\n\n');

% 运行优化算法
try
    optimized_isar_fusion();
    fprintf('\n=== 优化算法执行完成 ===\n');
    fprintf('请查看生成的图像文件:\n');
    fprintf('- optimized_ISAR_comparison_results.png: 优化版成像结果对比\n');
catch ME
    fprintf('错误: %s\n', ME.message);
    fprintf('错误位置: %s (第%d行)\n', ME.stack(1).name, ME.stack(1).line);
    
    % 提供调试信息
    fprintf('\n调试建议:\n');
    fprintf('1. 检查MATLAB版本和工具箱\n');
    fprintf('2. 确保内存充足\n');
    fprintf('3. 检查数据类型兼容性\n');
end
