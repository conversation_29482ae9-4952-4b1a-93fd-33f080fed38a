% 运行增强型VMD-ADMM-DCFT融合ISAR成像算法
% 这个脚本用于测试和验证改进的算法性能

clc; clear; close all;

fprintf('=== 启动增强型VMD-ADMM-DCFT融合ISAR成像算法测试 ===\n');
fprintf('算法特点:\n');
fprintf('1. 自适应VMD模态分解\n');
fprintf('2. 四阶DCFT相位估计\n');
fprintf('3. 频率权重ADMM重建\n');
fprintf('4. 智能融合策略\n');
fprintf('5. 旁瓣抑制后处理\n\n');

% 运行主算法
try
    enhanced_isar_fusion();
    fprintf('\n=== 算法执行完成 ===\n');
    fprintf('请查看生成的图像文件:\n');
    fprintf('- ISAR_comparison_results.png: 成像结果对比\n');
    fprintf('- ISAR_processing_steps.png: 中间处理步骤\n');
catch ME
    fprintf('错误: %s\n', ME.message);
    fprintf('错误位置: %s (第%d行)\n', ME.stack(1).name, ME.stack(1).line);
    
    % 提供调试信息
    fprintf('\n调试建议:\n');
    fprintf('1. 检查MATLAB版本是否支持所有函数\n');
    fprintf('2. 确保信号处理工具箱已安装\n');
    fprintf('3. 检查内存是否足够\n');
end
