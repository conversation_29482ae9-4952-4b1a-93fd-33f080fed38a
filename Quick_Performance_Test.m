%% VMD-ADMM-DCFT融合算法快速性能测试
%
% 本脚本用于快速测试优化后的融合算法性能
% 使用较小的数据集和优化参数进行快速验证
%
% 作者: ISAR算法研究团队
% 日期: 2024

clear; close all; clc;

fprintf('=== VMD-ADMM-DCFT融合算法快速性能测试 ===\n\n');

%% 1. 生成小规模测试数据
fprintf('1. 生成测试数据...\n');

% 使用较小的数据尺寸进行快速测试
num_range_bins = 64;   % 减少到64个距离单元
num_azimuth = 256;     % 减少到256个方位单元

echo_data = generate_test_data(num_range_bins, num_azimuth);
fprintf('测试数据尺寸: %d x %d\n', num_range_bins, num_azimuth);

%% 2. 配置优化参数
fprintf('\n2. 配置优化参数...\n');

params = struct();

% VMD参数 - 快速测试配置
params.vmd.K = 3;                    % 减少模态数
params.vmd.alpha = 2000;
params.vmd.tau = 0.1;
params.vmd.tol = 1e-5;               % 放宽收敛条件
params.vmd.max_iter = 100;           % 减少迭代次数

% DCFT参数 - 稀疏搜索
params.dcft.alpha_range = -32:32:320;    % 大步长搜索
params.dcft.beta_range = -1000:800:2400; % 大步长搜索
params.dcft.gamma_range = -500:500:1500; % 大步长搜索

% ADMM参数 - 快速收敛
params.admm.rho = 1.0;
params.admm.lambda = 0.1;
params.admm.max_iter = 30;           % 减少迭代次数
params.admm.tol = 1e-4;              % 放宽收敛条件

% 融合参数 - 快速测试
params.fusion.max_global_iter = 2;   % 只进行2次全局迭代
params.fusion.convergence_tol = 1e-2; % 放宽收敛条件
params.fusion.vmd_weight = 0.4;
params.fusion.dcft_weight = 0.3;
params.fusion.admm_weight = 0.3;

% 雷达参数
params.radar.fc = 5.2e9;
params.radar.B = 80e6;
params.radar.PRF = 1400;

% 处理参数
params.processing.dynamic_range_db = 30;

% 显示优化信息
alpha_count = length(params.dcft.alpha_range);
beta_count = length(params.dcft.beta_range);
gamma_count = length(params.dcft.gamma_range);
total_searches = alpha_count * beta_count * gamma_count;

fprintf('优化配置:\n');
fprintf('- VMD模态数: %d\n', params.vmd.K);
fprintf('- DCFT搜索点数: %d (原始约为 %d)\n', total_searches, total_searches*64);
fprintf('- 全局迭代次数: %d\n', params.fusion.max_global_iter);
fprintf('- 预期加速比: ~%.1fx\n', 64*5/2); % 估算加速比

%% 3. 性能基准测试
fprintf('\n3. 执行性能测试...\n');

% 测试传统FFT（基准）
fprintf('基准测试 - 传统FFT: ');
tic;
ISAR_fft = fftshift(fft(echo_data, [], 2), 2);
ISAR_fft_db = 20*log10(abs(ISAR_fft) / max(abs(ISAR_fft(:))));
fft_time = toc;
fprintf('%.3f 秒\n', fft_time);

% 测试优化后的融合算法
fprintf('主要测试 - 优化融合算法:\n');
fusion_timer = tic;
[ISAR_fusion, processing_info] = VMD_ADMM_DCFT_Fusion_ISAR(echo_data, params);
fusion_time = toc(fusion_timer);

%% 4. 性能分析
fprintf('\n=== 性能分析结果 ===\n');

% 时间性能
fprintf('处理时间对比:\n');
fprintf('- 传统FFT: %.3f 秒\n', fft_time);
fprintf('- 优化融合算法: %.3f 秒\n', fusion_time);
fprintf('- 时间比率: %.1fx\n', fusion_time / fft_time);

% 算法收敛性
fprintf('\n算法收敛性:\n');
fprintf('- 实际迭代次数: %d\n', processing_info.iterations);
fprintf('- 最终收敛误差: %.6f\n', processing_info.final_convergence);

% 图像质量对比
fusion_image = 10.^(ISAR_fusion/20);
fft_image = abs(ISAR_fft);

contrast_fusion = std(fusion_image(:)) / mean(fusion_image(:));
contrast_fft = std(fft_image(:)) / mean(fft_image(:));

entropy_fusion = calculate_entropy(fusion_image);
entropy_fft = calculate_entropy(fft_image);

fprintf('\n图像质量对比:\n');
fprintf('- 对比度提升: %.1f%% (%.4f vs %.4f)\n', ...
    (contrast_fusion - contrast_fft) / contrast_fft * 100, contrast_fusion, contrast_fft);
fprintf('- 熵降低: %.1f%% (%.4f vs %.4f)\n', ...
    (entropy_fft - entropy_fusion) / entropy_fft * 100, entropy_fusion, entropy_fft);

%% 5. 可视化结果
fprintf('\n5. 生成对比图像...\n');

figure('Name', '快速性能测试结果', 'Position', [100, 100, 1000, 600]);

% 融合算法结果
subplot(2, 3, 1);
imagesc(ISAR_fusion);
colormap(jet); colorbar;
caxis([-30, 0]);
title('优化融合算法');
xlabel('方位单元'); ylabel('距离单元');

% 传统FFT结果
subplot(2, 3, 2);
imagesc(ISAR_fft_db);
colormap(jet); colorbar;
caxis([-30, 0]);
title('传统FFT');
xlabel('方位单元'); ylabel('距离单元');

% 差异图
subplot(2, 3, 3);
difference = ISAR_fusion - ISAR_fft_db;
imagesc(difference);
colormap(jet); colorbar;
title('差异图 (融合 - FFT)');
xlabel('方位单元'); ylabel('距离单元');

% 处理时间对比
subplot(2, 3, 4);
bar([fft_time, fusion_time]);
set(gca, 'XTickLabel', {'FFT', '融合算法'});
ylabel('处理时间 (秒)');
title('处理时间对比');
grid on;

% 质量指标对比
subplot(2, 3, 5);
quality_data = [contrast_fft, contrast_fusion; entropy_fft, entropy_fusion];
bar(quality_data);
set(gca, 'XTickLabel', {'对比度', '熵'});
legend({'FFT', '融合算法'}, 'Location', 'best');
title('图像质量对比');
grid on;

% 收敛历史
if length(processing_info.convergence_history) > 1
    subplot(2, 3, 6);
    semilogy(processing_info.convergence_history, 'b-o', 'LineWidth', 2);
    grid on;
    title('收敛历史');
    xlabel('迭代次数'); ylabel('相对误差');
end

%% 6. 性能评估总结
fprintf('\n=== 性能评估总结 ===\n');

% 计算性能得分
time_score = max(0, 100 - (fusion_time / fft_time - 1) * 50); % 时间得分
quality_score = (contrast_fusion / contrast_fft - 1) * 100 + ...
                (entropy_fft / entropy_fusion - 1) * 100; % 质量得分
convergence_score = max(0, 100 - processing_info.final_convergence * 10000); % 收敛得分

overall_score = (time_score + quality_score + convergence_score) / 3;

fprintf('性能评分:\n');
fprintf('- 时间效率: %.1f/100\n', time_score);
fprintf('- 图像质量: %.1f/100\n', quality_score);
fprintf('- 收敛性能: %.1f/100\n', convergence_score);
fprintf('- 综合评分: %.1f/100\n', overall_score);

if overall_score >= 80
    fprintf('\n✅ 优秀! 算法性能优化效果显著\n');
elseif overall_score >= 60
    fprintf('\n✅ 良好! 算法性能达到预期\n');
elseif overall_score >= 40
    fprintf('\n⚠️  一般! 建议进一步优化参数\n');
else
    fprintf('\n❌ 需要改进! 算法性能有待提升\n');
end

% 优化建议
fprintf('\n优化建议:\n');
if fusion_time / fft_time > 10
    fprintf('- 考虑进一步减少DCFT搜索范围\n');
    fprintf('- 减少VMD模态数或迭代次数\n');
end
if contrast_fusion / contrast_fft < 1.1
    fprintf('- 调整融合权重以提高图像质量\n');
    fprintf('- 增加ADMM正则化强度\n');
end
if processing_info.final_convergence > 1e-3
    fprintf('- 检查收敛条件设置\n');
    fprintf('- 考虑增加迭代次数\n');
end

fprintf('\n=== 快速测试完成 ===\n');

%% ========== 辅助函数 ==========

function echo_data = generate_test_data(num_range_bins, num_azimuth)
%生成测试数据

% 简化的舰船模型
fc = 5.2e9;
PRF = 1400;
c = 3e8;

tm = (0:num_azimuth-1) / PRF;

% 舰船散射点
scatterers = [
    0, 0, 1.0;      % 中心
    -15, 8, 0.8;    % 舰首
    15, -8, 0.7;    % 舰尾
];

echo_data = zeros(num_range_bins, num_azimuth);

for i = 1:size(scatterers, 1)
    x0 = scatterers(i, 1);
    y0 = scatterers(i, 2);
    amp = scatterers(i, 3);
    
    % 简化运动模型
    omega = 0.05;
    x_t = x0 * cos(omega * tm);
    y_t = y0 * sin(omega * tm);
    R_t = sqrt(x_t.^2 + y_t.^2);
    
    % 相位历程
    phase = 4*pi*fc/c * R_t + pi * omega * tm.^2;
    signal = amp * exp(1j * phase);
    
    % 分配到距离单元
    range_bin = round(num_range_bins/2 + R_t(1)/5);
    range_bin = max(1, min(range_bin, num_range_bins));
    
    echo_data(range_bin, :) = echo_data(range_bin, :) + signal;
end

% 添加噪声
noise = 0.1 * (randn(size(echo_data)) + 1j * randn(size(echo_data)));
echo_data = echo_data + noise;

end

function entropy = calculate_entropy(image)
%计算图像熵

normalized = image / sum(image(:));
normalized(normalized == 0) = eps;
entropy = -sum(normalized(:) .* log2(normalized(:)));

end
