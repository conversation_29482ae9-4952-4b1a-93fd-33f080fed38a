# 混合DCFT-STVMD ISAR成像处理器

## 简介

混合DCFT-STVMD ISAR成像处理器是一个结合离散立方傅里叶变换(DCFT)和短时变分模态分解(STVMD)两种算法优势的ISAR成像工具。该处理器专为解决三维转动目标(如舰船)的成像问题而设计，能够同时适应仿真数据和实测数据，有效补偿舰船三维运动导致的运动误差，实现高分辨率成像。

## 技术原理

### DCFT和STVMD算法的区别与互补性

**DCFT (离散立方傅里叶变换):**
- 基于多项式相位模型，采用参数空间搜索的方式匹配目标的三维运动
- 优势：在仿真数据上表现良好，能有效处理预先建模的复杂运动
- 局限性：对实测数据中的不规则相位误差处理能力有限，需要精确的参数搜索范围

**STVMD (短时变分模态分解):**
- 基于信号分解的自适应相位补偿方法，无需预先知道运动模型
- 优势：对实测数据表现良好，能够自适应处理非平稳信号
- 局限性：在仿真数据上可能过度补偿，导致失真

### 融合策略

本处理器采用以下几种融合策略，以充分发挥两种算法的优势：

1. **自动模式选择**：根据数据特性自动选择最适合的算法或融合策略
2. **序贯处理**：先用DCFT进行粗略运动补偿，再用STVMD进行精细相位误差估计
3. **加权融合**：根据图像质量指标自适应调整两种算法结果的权重
4. **参数交互**：DCFT参数估计结果可以指导STVMD的初始化

## 主要特点

- **自适应数据类型检测**：自动区分仿真数据和实测数据
- **多模式处理**：提供多种处理模式，适应不同场景需求
- **参数优化**：针对舰船目标的三维运动特性优化参数设置
- **高精度相位补偿**：结合参数化模型和自适应分解实现精确相位补偿
- **图像质量评估**：提供多种图像质量指标，便于评估和比较
- **灵活的参数配置**：所有处理参数均可根据需要灵活调整

## 安装方法

将以下文件放置在同一目录下：
- `Hybrid_DCFT_STVMD_ISAR.m` - 混合ISAR处理器主函数
- `Run_Hybrid_ISAR.m` - 示例运行脚本
- `STVMD_ISAR.m` - STVMD算法函数（如果使用序贯或混合模式）

## 使用方法

### 基本用法

```matlab
% 加载数据
load('shipx2.mat');  % 加载距离压缩后的回波数据

% 创建参数结构体(使用默认值)
params = struct();
params.mode = 'auto';  % 自动选择处理模式

% 调用混合处理器
[ISAR_image, processing_info] = Hybrid_DCFT_STVMD_ISAR(shipx2, params);
```

### 完整示例

运行示例脚本：

```matlab
Run_Hybrid_ISAR
```

该脚本会自动尝试加载数据文件，设置合适的处理参数，调用混合处理器，并显示和保存结果。

## 参数配置

### 基本参数

```matlab
params.mode = 'auto';  % 处理模式：'auto'(自动),'dcft'(仅DCFT),'stvmd'(仅STVMD),'sequential'(序贯),'hybrid'(混合)
params.data_type = 'auto';  % 数据类型：'auto'(自动),'simulated'(仿真),'measured'(实测)
```

### 雷达参数

```matlab
params.radar.fc = 5.2e9;   % 载频 (Hz)
params.radar.B = 80e6;     % 带宽 (Hz)
params.radar.PRF = 1400;   % 脉冲重复频率 (Hz)
```

### 处理参数

```matlab
params.processing.range_bins = 'auto';  % 自动检测距离单元，也可以指定范围如30:56
params.processing.apply_window = true;  % 应用窗函数
params.processing.dynamic_range_db = 30;  % 显示动态范围 (dB)
```

### DCFT参数

```matlab
params.dcft.alpha_step = 8;    % 啁啾率搜索步长
params.dcft.alpha_min = -16;   % 最小啁啾率
params.dcft.alpha_max = 320;   % 最大啁啾率
params.dcft.beta_step = 100;   % 啁啾率导数搜索步长
params.dcft.beta_min = -500;   % 最小啁啾率导数
params.dcft.beta_max = 2400;   % 最大啁啾率导数
params.dcft.thresholding = true;  % 启用阈值处理
params.dcft.threshold_ratio = 0.2;  % 阈值比例
```

### STVMD参数

```matlab
params.stvmd.K = 3;  % 模态数量
params.stvmd.alpha = 2000;  % 平衡参数
params.stvmd.tau = 0.1;  % 拉格朗日乘子更新步长
params.stvmd.tol = 1e-7;  % 收敛容限
params.stvmd.window_sizes = [16, 32, 64];  % 多尺度窗口大小
params.stvmd.overlap = 0.5;  % 窗口重叠率
params.stvmd.dynamic = true;  % 是否使用动态中心频率
params.stvmd.max_iter = 500;  % 最大迭代次数
params.stvmd.global_iterations = 3;  % 全局迭代次数
```

### 融合参数

```matlab
params.fusion.method = 'sequential';  % 融合方法：'sequential'(序贯),'weighted'(加权)
params.fusion.weight_dcft = 0.5;  % DCFT权重 (仅用于加权融合)
params.fusion.weight_stvmd = 0.5;  % STVMD权重 (仅用于加权融合)
```

## 参数调优指南

### 数据类型判断

- 仿真数据通常具有明确的峰值和较规则的相位变化
- 实测数据通常含有不规则的相位扰动和更高的噪声水平
- 如果不确定，可以设置 `params.data_type = 'auto'`，让处理器自动判断

### 处理模式选择

1. **仿真数据推荐模式**：
   ```matlab
   params.mode = 'dcft';  % 仅使用DCFT处理
   ```

2. **实测数据推荐模式**：
   ```matlab
   params.mode = 'sequential';  % 先DCFT后STVMD
   ```

3. **通用模式**：
   ```matlab
   params.mode = 'auto';  % 自动选择最佳处理模式
   ```

### DCFT参数调整

DCFT参数的搜索范围是关键。根据目标的运动特性，可以采用不同的搜索范围：

1. **舰船目标**（默认）：
   ```matlab
   params.dcft.alpha_min = -16;
   params.dcft.alpha_max = 320;
   params.dcft.beta_min = -500;
   params.dcft.beta_max = 2400;
   ```

2. **飞机目标**（运动更快）：
   ```matlab
   params.dcft.alpha_min = -50;
   params.dcft.alpha_max = 500;
   params.dcft.beta_min = -1000;
   params.dcft.beta_max = 5000;
   ```

3. **计算效率与精度平衡**：
   - 快速处理：`alpha_step=16, beta_step=200`
   - 标准处理：`alpha_step=8, beta_step=100`
   - 精细处理：`alpha_step=4, beta_step=50`

### STVMD参数调整

STVMD参数需要根据信号特性进行调整：

1. **模态数量K**：
   - 较简单目标：`K=2`
   - 常规舰船：`K=3`（默认）
   - 复杂目标：`K=4`或`K=5`

2. **窗口大小**：
   - 较短脉冲序列：`window_sizes = [8, 16, 32]`
   - 常规长度：`window_sizes = [16, 32, 64]`（默认）
   - 较长脉冲序列：`window_sizes = [32, 64, 128]`

3. **平衡参数alpha**：
   - 较低信噪比：`alpha=1000`
   - 常规：`alpha=2000`（默认）
   - 较高信噪比：`alpha=3000`

### 融合参数调整

对于特定数据集，可以尝试不同的融合策略：

1. **序贯处理**（推荐）：
   ```matlab
   params.fusion.method = 'sequential';
   ```

2. **加权融合**：
   ```matlab
   params.fusion.method = 'weighted';
   params.fusion.weight_dcft = 0.6;  % 调整权重比例
   params.fusion.weight_stvmd = 0.4;
   ```

## 性能评估

处理器提供以下图像质量评估指标：

1. **对比度**：图像幅度的标准差与均值的比值，值越大表示图像越清晰
2. **熵**：图像的信息熵，值越小表示图像聚焦效果越好
3. **处理时间**：各算法的处理时间，用于评估计算效率

## 常见问题

1. **处理结果不理想**：
   - 尝试调整DCFT参数范围，特别是alpha和beta的搜索范围
   - 对于实测数据，增加STVMD的迭代次数和调整窗口大小

2. **处理时间过长**：
   - 增大DCFT的步长（alpha_step和beta_step）
   - 减少STVMD的迭代次数和窗口大小
   - 限制处理的距离单元范围

3. **无法正确加载数据**：
   - 确保数据格式为距离压缩后的复数据（距离×方位）
   - 检查数据变量名是否与代码中预期的一致

## 参考文献

1. Wu, D., Xu, M., Yu, Z. & Li, X. (2012). "ISAR Imaging of Targets With Complex Motions Based on the Keystone Transform." IEEE Geoscience and Remote Sensing Letters, 9(4), 749-753.
2. Dragomiretskiy, K., & Zosso, D. (2014). "Variational mode decomposition." IEEE Transactions on Signal Processing, 62(3), 531-544.
3. Chen, V. C., & Ling, H. (2002). "Time-frequency transforms for radar imaging and signal analysis." Artech House.
4. 张铮, 周盛, 胡伟. (2015). "基于改进的VMD算法的ISAR运动补偿". 电子学报, 43(8), 1520-1526.

## 致谢

感谢所有为本项目提供建议和支持的同事。特别感谢提供测试数据和参考算法的各位专家。 