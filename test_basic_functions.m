% 基本功能测试脚本
% 测试增强型ISAR算法的核心组件

clc; clear; close all;

fprintf('=== 增强型ISAR算法基本功能测试 ===\n');

% 1. 测试数据生成
fprintf('1. 测试数据生成...\n');
try
    [shipx2, radar_params] = generate_ship_data();
    fprintf('   ✓ 数据生成成功，尺寸: %dx%d\n', size(shipx2, 1), size(shipx2, 2));
catch ME
    fprintf('   ✗ 数据生成失败: %s\n', ME.message);
    return;
end

% 2. 测试参数配置
fprintf('2. 测试参数配置...\n');
try
    params = configure_enhanced_parameters();
    fprintf('   ✓ 参数配置成功\n');
    fprintf('   - VMD模态数: %d\n', params.vmd.K);
    fprintf('   - DCFT搜索范围: α[%d,%d], β[%d,%d]\n', ...
            params.dcft.alpha_range(1), params.dcft.alpha_range(2), ...
            params.dcft.beta_range(1), params.dcft.beta_range(2));
    fprintf('   - ADMM迭代次数: %d\n', params.admm.max_iter);
catch ME
    fprintf('   ✗ 参数配置失败: %s\n', ME.message);
    return;
end

% 3. 测试传统FFT成像
fprintf('3. 测试传统FFT成像...\n');
try
    tic;
    traditional_image = fftshift(fft(shipx2, [], 2), 2);
    traditional_time = toc;
    fprintf('   ✓ 传统FFT成像成功，耗时: %.2f秒\n', traditional_time);
catch ME
    fprintf('   ✗ 传统FFT成像失败: %s\n', ME.message);
    return;
end

% 4. 测试单个距离单元的VMD分解
fprintf('4. 测试VMD分解...\n');
try
    test_range_idx = floor(size(shipx2, 1) / 2);
    test_signal = shipx2(test_range_idx, :);
    
    if sum(abs(test_signal).^2) > 1e-10
        [vmd_modes, vmd_freqs] = enhanced_vmd_decomposition(test_signal, params.vmd);
        fprintf('   ✓ VMD分解成功，模态数: %d\n', size(vmd_modes, 1));
    else
        fprintf('   ! 测试信号能量过低，跳过VMD测试\n');
    end
catch ME
    fprintf('   ✗ VMD分解失败: %s\n', ME.message);
    fprintf('   错误详情: %s\n', ME.getReport);
end

% 5. 测试图像质量指标计算
fprintf('5. 测试图像质量指标...\n');
try
    contrast_val = calculate_contrast(traditional_image);
    entropy_val = calculate_entropy(abs(traditional_image));
    fprintf('   ✓ 质量指标计算成功\n');
    fprintf('   - 对比度: %.4f\n', contrast_val);
    fprintf('   - 熵值: %.4f\n', entropy_val);
catch ME
    fprintf('   ✗ 质量指标计算失败: %s\n', ME.message);
end

% 6. 测试基本可视化
fprintf('6. 测试基本可视化...\n');
try
    figure('Name', '基本功能测试结果', 'Position', [100, 100, 800, 400]);
    
    subplot(1, 2, 1);
    traditional_db = 20*log10(abs(traditional_image) / max(abs(traditional_image(:))));
    imagesc(traditional_db);
    caxis([-40, 0]);
    colormap('jet');
    colorbar;
    title('传统FFT成像结果');
    xlabel('方位单元');
    ylabel('距离单元');
    axis xy;
    
    subplot(1, 2, 2);
    % 显示原始数据幅度
    imagesc(20*log10(abs(shipx2) / max(abs(shipx2(:)))));
    caxis([-40, 0]);
    colormap('jet');
    colorbar;
    title('原始雷达数据');
    xlabel('方位单元');
    ylabel('距离单元');
    axis xy;
    
    saveas(gcf, 'basic_test_results.png');
    fprintf('   ✓ 基本可视化成功，结果保存为: basic_test_results.png\n');
catch ME
    fprintf('   ✗ 基本可视化失败: %s\n', ME.message);
end

fprintf('\n=== 基本功能测试完成 ===\n');
fprintf('如果所有测试都通过，可以运行完整的增强算法\n');

% 辅助函数定义
function [shipx2, radar_params] = generate_ship_data()
    % 简化的数据生成函数
    
    % 舰船散射点模型
    Pos = [-10 -1 0;-9 -1 0;-8 -1 0;-7 -1 0;-6 -1 0;-5 -1 0;-3.75 -1 0;-3 -1 0;-2 -1 0;-1 -1 0;...
           0 -1 0;...
           1 -1 0;2 -1 0;3 -1 0;4 -1 0;5 -1 0;6 -1 0;7 -1 0;8 -1 0;9 -1 0;10 -1 0;...
           -9.5 0.2 0.5;...
           -9.5 1.2 0.5;-9 1 0;-8 1 0;-7 1 0;-6 1 0;-5.2 1.2 0;-4.1 1 0;-3 1 0;-2 1 0;-1 1 0;...
           0 1 0;...
           1 1 0;2 1 0;3 1 0;4 1 0;5 1 0;6 1 0;7 1 0;8 1 0;9 1 0;10 1 0;...
           10.5 -0.75 0;10.5 0.75 0;11 -0.5 0;11 0.5 0;11.5 0 0;...
           9.5 0.5 0.5;9.5 -0.5 0.5;9 0 0.5;8.5 0.5 0.5;8.5 -0.5 0.5;...
           5 0 0.5;5 0 1;5 0 1.5;5 0 2;5 0 2.5;5 0 3;5 0 3.5;5 0 4;...
           5.5 0.5 0.5;5.5 -0.5 0.5;4.5 0.5 0.5;4.5 -0.5 0.5;...
           0.5 0.5 0.9;0.5 -0.5 0.9;-0.5 0.5 0.9;-0.5 -0.5 0.9;0 0 0.5;...
           -5 0 0.2;-5 0.2 0.8;-5 0.2 1.4;-5 0 2;...
           -5.5 -0.5 0.5;-5.5 0.5 0.5;-4.4 0.5 0.5;-4.5 -0.6 0.5;...
           ];
    
    % 坐标变换
    x_Pos = Pos(:,1)*5;
    y_Pos = Pos(:,2)*5;
    z_Pos = Pos(:,3)*5;
    min_x_Pos = min(x_Pos);
    x_Pos = x_Pos - min_x_Pos;
    min_y_Pos = min(y_Pos);
    y_Pos = y_Pos - min_y_Pos;
    min_z_Pos = min(z_Pos);
    z_Pos = z_Pos - min_z_Pos;
    
    % 雷达视线方向
    R = [cos(3*pi/8)*cos(0), cos(3*pi/8)*sin(0), sin(3*pi/8)];
    Num_point = length(x_Pos);
    
    % 计算相对位置
    x_r = zeros(1, Num_point);
    y_r = zeros(1, Num_point);
    z_r = zeros(1, Num_point);
    for n_point = 1:Num_point
        x_r(n_point) = y_Pos(n_point)*R(3) - z_Pos(n_point)*R(2);
        y_r(n_point) = z_Pos(n_point)*R(1) - x_Pos(n_point)*R(3);
        z_r(n_point) = x_Pos(n_point)*R(2) - y_Pos(n_point)*R(1);
    end
    
    % 运动参数
    x_omega = 0.05;
    y_omega = 0.2;
    z_omega = 0.05;
    
    x_lambda = 0.05;
    y_lambda = 0.1;
    z_lambda = 0.05;
    
    x_gamma = 0.05;
    y_gamma = 0.4;
    z_gamma = 0.05;
    
    % 计算运动参数
    f = zeros(1, Num_point);
    alpha = zeros(1, Num_point);
    beta = zeros(1, Num_point);
    for n_point = 1:Num_point
        f(n_point) = x_r(n_point)*x_omega + y_r(n_point)*y_omega + z_r(n_point)*z_omega;
        alpha(n_point) = x_r(n_point)*x_lambda + y_r(n_point)*y_lambda + z_r(n_point)*z_lambda;
        beta(n_point) = x_r(n_point)*x_gamma + y_r(n_point)*y_gamma + z_r(n_point)*z_gamma;
    end
    
    % 雷达参数
    B = 80*1e6;
    c = 3*1e8;
    PRF = 1400;
    fc = 5.2*1e9;
    delta_r = c/(2*B);
    r = -50*delta_r:delta_r:50*delta_r;
    tm = 0:(1/PRF):0.501;
    Num_r = length(r);
    Num_tm = length(tm);
    ones_r = ones(1, Num_r);
    ones_tm = ones(1, Num_tm);
    
    % 生成回波信号
    s_r_tm = 0;
    for n_point = 1:Num_point
        Delta_R0(n_point) = x_Pos(n_point)*R(1) + y_Pos(n_point)*R(2) + z_Pos(n_point)*R(3);
        Delta_R = f(n_point).*tm + (1/2)*alpha(n_point).*tm.^2 + (1/6)*beta(n_point).*tm.^3 + Delta_R0(n_point);
        sita_Delta_R = 4*pi*(fc/c)*Delta_R;
        
        % 散射强度
        if n_point > 53 && n_point < 62
            scattering_strength = 1.3;
        elseif n_point == 48
            scattering_strength = 1.2;
        else
            scattering_strength = 1.0;
        end
        
        s_r_tm = s_r_tm + scattering_strength * sinc((2*B/c)*(r.'*ones_tm - ones_r.'*Delta_R)) .* exp(1j*ones_r.'*sita_Delta_R);
    end
    
    shipx2 = s_r_tm;
    
    % 雷达参数
    radar_params = struct();
    radar_params.B = B;
    radar_params.c = c;
    radar_params.PRF = PRF;
    radar_params.fc = fc;
end
